'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { ThemeToggle } from '@/components/theme-toggle'
import { ContextMenu } from '@/components/ui/context-menu'
import { useRouter } from 'next/navigation'
import { 
  BarChart3, 
  Users, 
  Shield, 
  Zap, 
  ArrowRight, 
  Star, 
  CheckCircle,
  TrendingUp,
  Globe,
  Lock,
  ChevronLeft,
  ChevronRight,
  Gamepad2,
  Trophy,
  Target,
  X,
  Download,
  Code,
  Copy
} from 'lucide-react'

const AnimatedSection = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting)
      },
      { threshold: 0.1, rootMargin: '50px' }
    )

    const element = document.getElementById(className.split(' ')[0] || 'section')
    if (element) observer.observe(element)

    return () => observer.disconnect()
  }, [className])

  return (
    <div
      id={className.split(' ')[0] || 'section'}
      className={`transition-all duration-1000 ease-out transform ${
        isVisible 
          ? 'opacity-100 translate-y-0 scale-100' 
          : 'opacity-0 translate-y-8 scale-95'
      } ${className}`}
    >
      {children}
    </div>
  )
}

const FloatingCard = ({ children, delay = 0 }: { children: React.ReactNode, delay?: number }) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delay)
    return () => clearTimeout(timer)
  }, [delay])

  return (
    <div className={`transition-all duration-700 ease-out transform ${
      isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
    }`}>
      {children}
    </div>
  )
}

const ShinyText = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="relative inline-block">
      <div className="absolute inset-0 bg-gradient-to-r from-slate-100 via-blue-200 to-slate-100 bg-clip-text text-transparent">
        {children}
      </div>
      <div className="relative bg-gradient-to-r from-slate-100 via-blue-200 to-slate-100 bg-clip-text text-transparent animate-shine">
        {children}
      </div>
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-0 animate-shine-wave"></div>
    </div>
  )
}

const ScriptModal = ({ 
  isOpen, 
  onClose, 
  gameTitle 
}: { 
  isOpen: boolean
  onClose: () => void
  gameTitle: string 
}) => {
  const [copied, setCopied] = useState(false)

  const sampleScript = `-- ${gameTitle} Script
-- Generated by Pulse Hub

local Players = game:GetService("Players")
local player = Players.LocalPlayer

-- Auto-aim function
local function autoAim()
    print("Auto-aim activated for ${gameTitle}")
    -- Script implementation here
end

-- Speed boost function  
local function speedBoost()
    if player.Character and player.Character:FindFirstChild("Humanoid") then
        player.Character.Humanoid.WalkSpeed = 100
        print("Speed boost activated!")
    end
end

-- Main execution
autoAim()
speedBoost()
print("${gameTitle} script loaded successfully!")`

  const copyScript = () => {
    navigator.clipboard.writeText(sampleScript)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div 
        className="absolute inset-0 bg-black/60 backdrop-blur-sm animate-in fade-in-0 duration-300"
        onClick={onClose}
      />
      <div className="relative bg-slate-800/90 backdrop-blur-lg border border-slate-700/50 rounded-2xl p-6 w-full max-w-2xl max-h-[80vh] overflow-auto animate-in zoom-in-95 fade-in-0 duration-300">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <Code className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-slate-200">{gameTitle} Script</h3>
              <p className="text-sm text-slate-400">Ready to download and use</p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onClose}
            className="rounded-full border-slate-600/50 text-slate-400 hover:bg-slate-700/50 hover:text-slate-200"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        <div className="bg-slate-900/50 rounded-xl p-4 mb-6 border border-slate-700/30">
          <pre className="text-sm text-slate-300 whitespace-pre-wrap overflow-x-auto">
            {sampleScript}
          </pre>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={copyScript}
            className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-400 hover:to-purple-400 text-white rounded-xl"
          >
            {copied ? (
              <>
                <CheckCircle className="w-4 h-4 mr-2" />
                Copied!
              </>
            ) : (
              <>
                <Copy className="w-4 h-4 mr-2" />
                Copy Script
              </>
            )}
          </Button>
          <Button
            variant="outline"
            className="flex-1 border-slate-600/50 text-slate-300 hover:bg-slate-700/50 hover:text-slate-200 rounded-xl"
          >
            <Download className="w-4 h-4 mr-2" />
            Download
          </Button>
        </div>

        <div className="mt-6 p-4 bg-amber-500/10 border border-amber-500/20 rounded-xl">
          <div className="flex items-start gap-3">
            <div className="w-5 h-5 bg-amber-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-amber-400 text-xs font-bold">!</span>
            </div>
            <div>
              <h4 className="text-amber-200 font-medium mb-1">Usage Notice</h4>
              <p className="text-amber-300/80 text-sm">
                Use scripts responsibly and in accordance with game terms of service. 
                Pulse Hub is not responsible for any account actions taken by game moderators.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const GameCard = ({
  title,
  description,
  icon: Icon,
  isCenter = false,
  isLeft = false,
  isRight = false,
  features = ['Advanced AI', 'Multiplayer Support', 'Cross-Platform'],
  rating = '4.8',
  onGetScript,
  animationClass = '',
  gameIndex
}: {
  title: string
  description: string
  icon: any
  isCenter?: boolean
  isLeft?: boolean
  isRight?: boolean
  features?: string[]
  rating?: string
  onGetScript?: () => void
  animationClass?: string
  gameIndex?: number
}) => {
  const getCardClasses = () => {
    if (isCenter) {
      return "bg-gradient-to-br from-blue-600/20 to-purple-600/20 border-blue-400/40 shadow-2xl shadow-blue-500/20"
    }
    if (isLeft || isRight) {
      return "bg-gradient-to-br from-slate-700/20 to-slate-600/20 border-slate-600/30"
    }
    return "bg-gradient-to-br from-slate-800/20 to-slate-700/20 border-slate-700/30"
  }

  return (
    <div
      className={`
        relative rounded-3xl border backdrop-blur-sm cursor-pointer
        w-80 h-[480px] flex flex-col overflow-hidden transform-style-preserve-3d backface-hidden
        transition-all duration-700 ease-out
        ${getCardClasses()} ${animationClass}
      `}
    >
      <div className={`
        relative h-48 flex items-center justify-center transition-all duration-700
        ${isCenter ? 'bg-gradient-to-br from-blue-500/30 to-purple-500/30' : 'bg-gradient-to-br from-slate-600/30 to-slate-700/30'}
      `}>
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
        <Icon className={`w-16 h-16 z-10 transition-all duration-500 ${
          isCenter ? 'text-blue-300 drop-shadow-lg' : 'text-slate-400'
        }`} />
        <div className={`
          absolute top-4 left-4 px-3 py-1 rounded-full text-xs font-medium border transition-all duration-500
          ${isCenter ? 'bg-blue-500/20 border-blue-400/30 text-blue-200' : 'bg-slate-700/30 border-slate-600/30 text-slate-400'}
        `}>
          IMAGE
        </div>
      </div>

      <div className="flex-1 p-6 flex flex-col">
        <div className="mb-4">
          <h3 className={`text-lg font-bold mb-2 transition-all duration-500 ${
            isCenter ? 'text-blue-200' : 'text-slate-300'
          }`}>
            Features
          </h3>
          <ul className="space-y-1">
            {features.map((feature, index) => (
              <li key={index} className={`text-sm flex items-center gap-2 transition-all duration-500 ${
                isCenter ? 'text-blue-300/80' : 'text-slate-400'
              }`}>
                <div className={`w-1.5 h-1.5 rounded-full ${
                  isCenter ? 'bg-blue-400' : 'bg-slate-500'
                }`}></div>
                {feature}
              </li>
            ))}
          </ul>
        </div>

        <div className="mt-auto">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h4 className={`font-bold text-base transition-all duration-500 ${
                isCenter ? 'text-blue-200' : 'text-slate-300'
              }`}>
                {title}
              </h4>
              <div className="flex items-center gap-1 mt-1">
                <span className={`text-sm font-medium transition-all duration-500 ${
                  isCenter ? 'text-blue-300' : 'text-slate-400'
                }`}>
                  RATING
                </span>
                <div className="flex items-center gap-1">
                  <Star className={`w-3 h-3 fill-current ${
                    isCenter ? 'text-yellow-400' : 'text-slate-500'
                  }`} />
                  <span className={`text-sm font-bold ${
                    isCenter ? 'text-yellow-400' : 'text-slate-500'
                  }`}>
                    {rating}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <Button
            size="sm"
            onClick={onGetScript}
            className={`w-full rounded-xl transition-all duration-500 ${
              isCenter 
                ? 'bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-400 hover:to-purple-400 text-white shadow-lg shadow-blue-500/20' 
                : 'bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 border border-slate-600/30'
            }`}
          >
            GET SCRIPT
          </Button>
        </div>
      </div>
    </div>
  )
}

export default function HomePage() {
  const [scrolled, setScrolled] = useState(false)
  const [activeSection, setActiveSection] = useState('hero')
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number; selectedText?: string } | null>(null)
  const [currentGameIndex, setCurrentGameIndex] = useState(2)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedGame, setSelectedGame] = useState('')
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [animationDirection, setAnimationDirection] = useState<'next' | 'prev' | 'direct'>('next')
  // Remove animation state variables as we're using CSS transitions
  const router = useRouter()

  const games = [
    { 
      title: 'Pulse Arena', 
      description: 'Competitive battle arena with advanced combat systems.',
      icon: Target,
      features: ['Auto-aim', 'ESP/Wallhack', 'Damage Multiplier', 'God Mode'],
      rating: '4.9'
    },
    { 
      title: 'Pulse Racing', 
      description: 'High-speed racing with stunning visual effects.',
      icon: Zap,
      features: ['Speed Boost', 'Teleport', 'No Collision', 'Unlimited Fuel'],
      rating: '4.7'
    },
    { 
      title: 'Pulse Quest', 
      description: 'Epic adventure through mystical realms.',
      icon: Trophy,
      features: ['Quest Skip', 'Unlimited Items', 'Level Boost', 'Auto-Farm'],
      rating: '4.8'
    },
    { 
      title: 'Pulse Tactics', 
      description: 'Strategic warfare with team-based gameplay.',
      icon: Shield,
      features: ['Tactical ESP', 'Resource Hack', 'Unit Boost', 'Map Reveal'],
      rating: '4.6'
    },
    { 
      title: 'Pulse World', 
      description: 'Open-world exploration and building.',
      icon: Gamepad2,
      features: ['Fly Mode', 'Build Assist', 'Unlimited Resources', 'Weather Control'],
      rating: '4.9'
    }
  ]

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50)
      
      const sections = ['hero', 'features', 'games', 'stats', 'testimonials', 'cta']
      const currentSection = sections.find(section => {
        const element = document.getElementById(section)
        if (element) {
          const rect = element.getBoundingClientRect()
          return rect.top <= 100 && rect.bottom >= 100
        }
        return false
      })
      
      if (currentSection) setActiveSection(currentSection)
    }

    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault()
      const selectedText = window.getSelection()?.toString()
      setContextMenu({
        x: e.clientX,
        y: e.clientY,
        selectedText
      })
    }

    const handleClick = () => {
      setContextMenu(null)
    }

    // Keyboard navigation for carousel
    const handleKeyDown = (e: KeyboardEvent) => {
      if (activeSection === 'games' && !isTransitioning) {
        if (e.key === 'ArrowLeft') {
          e.preventDefault()
          prevGame()
        } else if (e.key === 'ArrowRight') {
          e.preventDefault()
          nextGame()
        } else if (e.key >= '1' && e.key <= '9') {
          const index = parseInt(e.key) - 1
          if (index < games.length) {
            e.preventDefault()
            goToGame(index)
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    document.addEventListener('contextmenu', handleContextMenu)
    document.addEventListener('click', handleClick)
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      window.removeEventListener('scroll', handleScroll)
      document.removeEventListener('contextmenu', handleContextMenu)
      document.removeEventListener('click', handleClick)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [activeSection, games.length, isTransitioning])

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ 
      behavior: 'smooth',
      block: 'start'
    })
  }

  const nextGame = () => {
    if (isTransitioning) return
    setIsTransitioning(true)
    setAnimationDirection('next')

    // Add a small delay to ensure smooth animation
    requestAnimationFrame(() => {
      setCurrentGameIndex((prev) => (prev + 1) % games.length)
    })

    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false)
    }, 800) // Match CSS transition duration
  }

  const prevGame = () => {
    if (isTransitioning) return
    setIsTransitioning(true)
    setAnimationDirection('prev')

    // Add a small delay to ensure smooth animation
    requestAnimationFrame(() => {
      setCurrentGameIndex((prev) => (prev - 1 + games.length) % games.length)
    })

    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false)
    }, 800) // Match CSS transition duration
  }

  const goToGame = (index: number) => {
    if (isTransitioning || index === currentGameIndex) return
    setIsTransitioning(true)
    setAnimationDirection('direct')

    // Add a small delay to ensure smooth animation
    requestAnimationFrame(() => {
      setCurrentGameIndex(index)
    })

    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false)
    }, 800) // Match CSS transition duration
  }

  const handleGetScript = (gameTitle: string) => {
    setSelectedGame(gameTitle)
    setIsModalOpen(true)
  }

  // Define position transforms for smooth transitions
  const positionTransforms = {
    'far-left': 'translateX(-640px) translateZ(-100px) rotateY(-25deg) scale(0.7)',
    'left': 'translateX(-320px) translateZ(0px) rotateY(-15deg) scale(0.9)',
    'center': 'translateX(0) translateZ(100px) rotateY(0deg) scale(1.1)',
    'right': 'translateX(320px) translateZ(0px) rotateY(15deg) scale(0.9)',
    'far-right': 'translateX(640px) translateZ(-100px) rotateY(25deg) scale(0.7)',
    'hidden-left': 'translateX(-960px) translateZ(-200px) rotateY(-35deg) scale(0.5)',
    'hidden-right': 'translateX(960px) translateZ(-200px) rotateY(35deg) scale(0.5)'
  }

  const positionZIndex = {
    'far-left': 10,
    'left': 20,
    'center': 30,
    'right': 20,
    'far-right': 10,
    'hidden-left': 5,
    'hidden-right': 5
  }

  const positionOpacity = {
    'far-left': 0.4,
    'left': 1,
    'center': 1,
    'right': 1,
    'far-right': 0.4,
    'hidden-left': 0,
    'hidden-right': 0
  }

  const getPositionName = (relativePosition: number) => {
    switch(relativePosition) {
      case -2: return 'far-left'
      case -1: return 'left'
      case 0: return 'center'
      case 1: return 'right'
      case 2: return 'far-right'
      case -3: return 'hidden-left'
      case 3: return 'hidden-right'
      default: return relativePosition < 0 ? 'hidden-left' : 'hidden-right'
    }
  }

  const getAllGamesWithPositions = () => {
    return games.map((game, gameIndex) => {
      // Calculate position relative to current center
      let relativePosition = gameIndex - currentGameIndex

      // Handle wrapping for smooth circular transitions
      if (relativePosition > games.length / 2) {
        relativePosition -= games.length
      } else if (relativePosition < -games.length / 2) {
        relativePosition += games.length
      }

      const positionName = getPositionName(relativePosition)
      return {
        game: game,
        position: relativePosition,
        index: gameIndex,
        positionName: positionName,
        transform: positionTransforms[positionName],
        zIndex: positionZIndex[positionName],
        opacity: positionOpacity[positionName]
      }
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-indigo-950 relative">
      <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-slate-800/20"></div>
      
      <style jsx>{`
        @keyframes shine {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }
        
        @keyframes shine-wave {
          0% { 
            transform: translateX(-100%);
            opacity: 0;
          }
          50% { 
            opacity: 1;
          }
          100% { 
            transform: translateX(100%);
            opacity: 0;
          }
        }
        
        .animate-shine {
          background-size: 200% 100%;
          animation: shine 3s infinite;
        }
        
        .animate-shine-wave {
          animation: shine-wave 3s infinite;
        }
        
        .rotate-y-12 {
          transform: perspective(1000px) rotateY(12deg);
        }
        
        .-rotate-y-12 {
          transform: perspective(1000px) rotateY(-12deg);
        }
        
        .perspective-1000 {
          perspective: 1000px;
        }
      `}</style>
      
      <div className="relative z-10">
        <nav className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-500 ease-out ${
          scrolled 
            ? 'bg-slate-800/40 backdrop-blur-lg border border-slate-700/30 shadow-lg shadow-black/20 rounded-full px-6 py-3' 
            : 'bg-transparent rounded-full px-6 py-4'
        }`}>
          <div className="flex items-center gap-8">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center">
                <BarChart3 className="w-4 h-4 text-white" />
              </div>
              <span className="font-bold text-lg bg-gradient-to-r from-blue-200 to-blue-300 bg-clip-text text-transparent">
                Pulse Hub
              </span>
            </div>
            
            <div className="hidden md:flex items-center gap-6">
              {[
                { id: 'hero', label: 'Home' },
                { id: 'features', label: 'Features' },
                { id: 'games', label: 'Games' },
                { id: 'stats', label: 'Analytics' },
                { id: 'testimonials', label: 'Reviews' }
              ].map(({ id, label }) => (
                <button
                  key={id}
                  onClick={() => scrollToSection(id)}
                  className={`text-sm font-medium transition-all duration-300 hover:text-blue-200 relative ${
                    activeSection === id ? 'text-blue-200' : 'text-slate-300'
                  }`}
                >
                  {label}
                  {activeSection === id && (
                    <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full" />
                  )}
                </button>
              ))}
            </div>

            <div className="flex items-center gap-3">
              <ThemeToggle />
              <Button
                onClick={() => router.push('/login')}
                variant="outline"
                size="sm"
                className="rounded-full border-slate-600/50 text-slate-300 hover:bg-slate-700/30 hover:border-slate-500/50 hover:text-slate-200 transition-all duration-300"
              >
                Login
              </Button>
              <Button
                onClick={() => router.push('/dashboard')}
                size="sm"
                className="rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-400 hover:to-blue-500 transition-all duration-300 shadow-lg shadow-blue-900/30"
              >
                Dashboard
              </Button>
            </div>
          </div>
        </nav>

        <AnimatedSection className="hero min-h-screen flex items-center justify-center pt-20">
          <div className="container mx-auto px-6 text-center">
            <FloatingCard>
              <div className="inline-flex items-center gap-2 bg-slate-800/50 backdrop-blur-sm rounded-full px-4 py-2 mb-8 border border-slate-700/30">
                <Star className="w-4 h-4 text-yellow-400" />
                <span className="text-sm text-slate-300">Trusted by 10,000+ users</span>
              </div>
            </FloatingCard>

            <FloatingCard delay={200}>
              <h1 className="text-6xl md:text-8xl font-bold mb-6 leading-tight">
                <ShinyText>Pulse Hub</ShinyText>
              </h1>
            </FloatingCard>

            <FloatingCard delay={400}>
              <p className="text-xl md:text-2xl text-slate-300 mb-8 max-w-3xl mx-auto leading-relaxed">
                The most powerful admin dashboard for modern applications. 
                Monitor, manage, and scale your platform with elegant simplicity.
              </p>
            </FloatingCard>

            <FloatingCard delay={600}>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button
                  onClick={() => router.push('/register')}
                  size="lg"
                  className="rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-400 hover:to-blue-500 transition-all duration-300 shadow-lg shadow-blue-900/30 px-8 py-6 text-lg"
                >
                  Get Started Free
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
                <Button
                  onClick={() => scrollToSection('features')}
                  variant="outline"
                  size="lg"
                  className="rounded-full border-slate-600/50 text-slate-300 hover:bg-slate-700/30 hover:border-slate-500/50 hover:text-slate-200 transition-all duration-300 px-8 py-6 text-lg"
                >
                  Learn More
                </Button>
              </div>
            </FloatingCard>

            <FloatingCard delay={800}>
              <div className="mt-16 relative">
                <div className="bg-slate-800/30 backdrop-blur-sm rounded-2xl p-8 border border-slate-700/30 shadow-2xl shadow-black/20 max-w-4xl mx-auto">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {[
                      { icon: Users, label: 'User Management', color: 'text-blue-400' },
                      { icon: BarChart3, label: 'Analytics', color: 'text-emerald-400' },
                      { icon: Shield, label: 'Security', color: 'text-purple-400' }
                    ].map(({ icon: Icon, label, color }, index) => (
                      <div key={label} className="flex items-center gap-3">
                        <div className={`w-12 h-12 rounded-xl bg-slate-700/50 flex items-center justify-center ${color}`}>
                          <Icon className="w-6 h-6" />
                        </div>
                        <span className="text-slate-300">{label}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </FloatingCard>
          </div>
        </AnimatedSection>

        <AnimatedSection className="features py-32 bg-gradient-to-b from-transparent to-slate-800/20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-slate-100 to-blue-200 bg-clip-text text-transparent">
                Powerful Features
              </h2>
              <p className="text-xl text-slate-400 max-w-2xl mx-auto">
                Everything you need to manage your platform efficiently and effectively
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: Users,
                  title: 'User Management',
                  description: 'Complete user lifecycle management with advanced permissions and role-based access control.',
                  color: 'from-blue-900/30 to-blue-800/30 border-blue-700/30'
                },
                {
                  icon: BarChart3,
                  title: 'Advanced Analytics',
                  description: 'Real-time insights and detailed reports to help you make data-driven decisions.',
                  color: 'from-emerald-900/30 to-emerald-800/30 border-emerald-700/30'
                },
                {
                  icon: Shield,
                  title: 'Enterprise Security',
                  description: 'Bank-grade security with encryption, audit logs, and compliance features.',
                  color: 'from-purple-900/30 to-purple-800/30 border-purple-700/30'
                },
                {
                  icon: Zap,
                  title: 'Lightning Fast',
                  description: 'Optimized performance with instant loading and real-time updates.',
                  color: 'from-yellow-900/30 to-yellow-800/30 border-yellow-700/30'
                },
                {
                  icon: Globe,
                  title: 'Global Scale',
                  description: 'Built to handle millions of users across multiple regions and time zones.',
                  color: 'from-cyan-900/30 to-cyan-800/30 border-cyan-700/30'
                },
                {
                  icon: Lock,
                  title: 'Privacy First',
                  description: 'GDPR compliant with granular privacy controls and data protection.',
                  color: 'from-red-900/30 to-red-800/30 border-red-700/30'
                }
              ].map(({ icon: Icon, title, description, color }, index) => (
                <FloatingCard key={title} delay={index * 100}>
                  <div className={`p-8 rounded-2xl bg-gradient-to-br ${color} border backdrop-blur-sm hover:scale-105 transition-all duration-300 group cursor-pointer`}>
                    <Icon className="w-12 h-12 text-slate-300 mb-4 group-hover:scale-110 transition-transform duration-300" />
                    <h3 className="text-xl font-semibold mb-3 text-slate-200">{title}</h3>
                    <p className="text-slate-400 leading-relaxed">{description}</p>
                  </div>
                </FloatingCard>
              ))}
            </div>
          </div>
        </AnimatedSection>

        <AnimatedSection id="games" className="games py-32 bg-gradient-to-b from-slate-800/20 to-transparent relative overflow-hidden">
          {/* Background 3D Elements */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/20 rounded-full blur-xl"></div>
            <div className="absolute bottom-20 right-10 w-40 h-40 bg-purple-500/20 rounded-full blur-xl"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full blur-3xl"></div>
          </div>

          <div className="container mx-auto px-6 relative z-10">
            <div className="text-center mb-20">
              <FloatingCard>
                <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-slate-100 via-blue-200 to-purple-200 bg-clip-text text-transparent">
                  Our Games
                </h2>
              </FloatingCard>
              <FloatingCard delay={200}>
                <p className="text-xl text-slate-400 max-w-2xl mx-auto">
                  Explore our collection of premium game scripts designed for the ultimate gaming experience.
                </p>
              </FloatingCard>
              <FloatingCard delay={400}>
                <div className="flex items-center justify-center gap-4 mt-6">
                  <div className="flex items-center gap-2 text-sm text-slate-500">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <span>3D Interactive Carousel</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-slate-500">
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                    <span>Rolling Animation</span>
                  </div>
                </div>
              </FloatingCard>
            </div>

            <div className="relative">
              {/* Carousel Stage with Enhanced 3D Effects */}
              <div className={`carousel-container relative ${
                isTransitioning ? 'scale-95 opacity-70' : 'scale-100 opacity-100'
              } transition-all duration-300`}>
                {/* 3D Stage Background */}
                <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-900/10 to-transparent rounded-3xl"></div>
                <div className="absolute inset-x-0 bottom-0 h-32 bg-gradient-to-t from-slate-900/20 to-transparent rounded-b-3xl"></div>

                {/* Main Carousel Track */}
                <div className="carousel-track flex items-center justify-center relative h-[520px] py-4">
                  {games.map((game, index) => {
                    const relativePosition = index - currentGameIndex

                    // Handle wrapping for smooth circular transitions
                    let adjustedPosition = relativePosition
                    if (adjustedPosition > games.length / 2) {
                      adjustedPosition -= games.length
                    } else if (adjustedPosition < -games.length / 2) {
                      adjustedPosition += games.length
                    }

                    const getPositionClass = () => {
                      switch(adjustedPosition) {
                        case 0: return 'card-position-center'
                        case -1: return 'card-position-left'
                        case 1: return 'card-position-right'
                        case -2: return 'card-position-far-left'
                        case 2: return 'card-position-far-right'
                        default: return adjustedPosition < 0 ? 'card-position-hidden-left' : 'card-position-hidden-right'
                      }
                    }

                    return (
                      <div
                        key={`game-${game.title}`}
                        className={`absolute ${getPositionClass()} ${adjustedPosition === 0 ? 'center-card-float' : ''}`}
                      >
                        <GameCard
                          title={game.title}
                          description={game.description}
                          icon={game.icon}
                          isCenter={adjustedPosition === 0}
                          isLeft={adjustedPosition === -1}
                          isRight={adjustedPosition === 1}
                          features={game.features}
                          rating={game.rating}
                          onGetScript={() => handleGetScript(game.title)}
                          animationClass="carousel-card"
                          gameIndex={index}
                        />
                      </div>
                    )
                  })}
                </div>
              </div>

              {/* Enhanced Navigation Buttons */}
              <Button
                variant="outline"
                size="lg"
                onClick={prevGame}
                disabled={isTransitioning}
                className="absolute left-4 top-1/2 -translate-y-1/2 rounded-full border-slate-600/50 text-slate-400 hover:bg-slate-700/50 hover:text-slate-200 disabled:opacity-50 backdrop-blur-sm bg-slate-800/30 hover:bg-slate-700/60 transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-blue-500/20"
              >
                <ChevronLeft className="w-6 h-6" />
              </Button>

              <Button
                variant="outline"
                size="lg"
                onClick={nextGame}
                disabled={isTransitioning}
                className="absolute right-4 top-1/2 -translate-y-1/2 rounded-full border-slate-600/50 text-slate-400 hover:bg-slate-700/50 hover:text-slate-200 disabled:opacity-50 backdrop-blur-sm bg-slate-800/30 hover:bg-slate-700/60 transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-blue-500/20"
              >
                <ChevronRight className="w-6 h-6" />
              </Button>

              {/* Enhanced Indicator Dots */}
              <div className="flex justify-center gap-3 mt-12">
                {games.map((game, index) => (
                  <button
                    key={index}
                    onClick={() => goToGame(index)}
                    disabled={isTransitioning}
                    className={`group relative transition-all duration-300 ${
                      index === currentGameIndex
                        ? 'scale-125'
                        : 'hover:scale-110'
                    }`}
                    title={game.title}
                  >
                    <div className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentGameIndex
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 shadow-lg shadow-blue-500/50'
                        : 'bg-slate-600 hover:bg-slate-500 group-hover:shadow-md group-hover:shadow-slate-400/30'
                    }`}></div>
                    {index === currentGameIndex && (
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse opacity-30"></div>
                    )}
                  </button>
                ))}
              </div>

              {/* Game Counter and Controls Info */}
              <div className="text-center mt-6 space-y-2">
                <span className="text-sm text-slate-500">
                  {currentGameIndex + 1} of {games.length}
                </span>
                <div className="flex items-center justify-center gap-4 text-xs text-slate-600">
                  <span className="flex items-center gap-1">
                    <kbd className="px-2 py-1 bg-slate-800/50 rounded text-slate-400">←</kbd>
                    <kbd className="px-2 py-1 bg-slate-800/50 rounded text-slate-400">→</kbd>
                    Navigate
                  </span>
                  <span className="flex items-center gap-1">
                    <kbd className="px-2 py-1 bg-slate-800/50 rounded text-slate-400">1-{games.length}</kbd>
                    Jump to game
                  </span>
                </div>
              </div>
            </div>
          </div>
        </AnimatedSection>

        <AnimatedSection className="stats py-32">
          <div className="container mx-auto px-6">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-slate-200">
                Trusted Worldwide
              </h2>
              <p className="text-xl text-slate-400">
                Join thousands of companies already using Pulse Hub
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {[
                { number: '10K+', label: 'Active Users', icon: Users },
                { number: '99.9%', label: 'Uptime', icon: TrendingUp },
                { number: '500+', label: 'Companies', icon: Globe },
                { number: '24/7', label: 'Support', icon: Shield }
              ].map(({ number, label, icon: Icon }, index) => (
                <FloatingCard key={label} delay={index * 150}>
                  <div className="text-center group hover:scale-105 transition-all duration-300">
                    <div className="w-16 h-16 mx-auto mb-4 bg-slate-800/50 rounded-2xl flex items-center justify-center group-hover:bg-slate-700/50 transition-all duration-300">
                      <Icon className="w-8 h-8 text-blue-400" />
                    </div>
                    <div className="text-3xl md:text-4xl font-bold text-slate-200 mb-2">{number}</div>
                    <div className="text-slate-400">{label}</div>
                  </div>
                </FloatingCard>
              ))}
            </div>
          </div>
        </AnimatedSection>

        <AnimatedSection className="testimonials py-32 bg-gradient-to-b from-slate-800/20 to-transparent">
          <div className="container mx-auto px-6">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-slate-200">
                What Our Users Say
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  quote: "Pulse Hub transformed how we manage our platform. The analytics are incredible and the interface is beautiful.",
                  author: "Sarah Chen",
                  role: "CTO, TechFlow"
                },
                {
                  quote: "The best admin dashboard we've ever used. Clean, powerful, and everything just works perfectly.",
                  author: "Marcus Johnson",
                  role: "Product Manager, DataSync"
                },
                {
                  quote: "Incredible attention to detail and the support team is amazing. Highly recommend to any growing company.",
                  author: "Emily Rodriguez",
                  role: "CEO, GrowthLab"
                }
              ].map(({ quote, author, role }, index) => (
                <FloatingCard key={author} delay={index * 200}>
                  <div className="bg-slate-800/30 backdrop-blur-sm border border-slate-700/30 rounded-2xl p-8 hover:scale-105 transition-all duration-300 group">
                    <div className="flex mb-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <blockquote className="text-lg mb-6 leading-relaxed text-slate-300">"{quote}"</blockquote>
                    <div>
                      <div className="font-semibold text-slate-200">{author}</div>
                      <div className="text-sm text-slate-400">{role}</div>
                    </div>
                  </div>
                </FloatingCard>
              ))}
            </div>
          </div>
        </AnimatedSection>

        <AnimatedSection className="cta py-32">
          <div className="container mx-auto px-6 text-center">
            <FloatingCard>
              <div className="max-w-4xl mx-auto">
                <h2 className="text-4xl md:text-6xl font-bold mb-8 bg-gradient-to-r from-slate-100 via-blue-200 to-slate-100 bg-clip-text text-transparent">
                  Ready to Get Started?
                </h2>
                <p className="text-xl text-slate-400 mb-12 max-w-2xl mx-auto">
                  Join thousands of companies using Pulse Hub to manage their platforms more effectively.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                  <Button
                    onClick={() => router.push('/register')}
                    size="lg"
                    className="rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-400 hover:to-blue-500 transition-all duration-300 shadow-lg shadow-blue-900/30 px-12 py-6 text-lg"
                  >
                    Start Free Trial
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Button>
                  <Button
                    onClick={() => router.push('/dashboard')}
                    variant="outline"
                    size="lg"
                    className="rounded-full border-slate-600/50 text-slate-300 hover:bg-slate-700/30 hover:border-slate-500/50 hover:text-slate-200 transition-all duration-300 px-12 py-6 text-lg"
                  >
                    View Dashboard
                  </Button>
                </div>

                <div className="mt-12 flex items-center justify-center gap-6 text-sm text-slate-400">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-emerald-400" />
                    <span>No credit card required</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-emerald-400" />
                    <span>14-day free trial</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-emerald-400" />
                    <span>Cancel anytime</span>
                  </div>
                </div>
              </div>
            </FloatingCard>
          </div>
        </AnimatedSection>

        <footer className="py-16 border-t border-slate-700/30 bg-slate-800/20">
          <div className="container mx-auto px-6">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="flex items-center gap-2 mb-4 md:mb-0">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center">
                  <BarChart3 className="w-4 h-4 text-white" />
                </div>
                <span className="font-bold text-lg text-slate-200">Pulse Hub</span>
              </div>
              <div className="text-sm text-slate-400">
                © 2024 Pulse Hub. All rights reserved.
              </div>
            </div>
          </div>
        </footer>

        {contextMenu && (
          <ContextMenu
            x={contextMenu.x}
            y={contextMenu.y}
            selectedText={contextMenu.selectedText}
            onClose={() => setContextMenu(null)}
          />
        )}

        <ScriptModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          gameTitle={selectedGame}
        />
      </div>
    </div>
  )
}

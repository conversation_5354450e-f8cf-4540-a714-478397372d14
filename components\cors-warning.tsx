'use client'

import { useState, useEffect } from 'react'
import { Alert<PERSON>riangle, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface CorsWarningProps {
  isVisible: boolean
  onClose: () => void
}

export function CorsWarning({ isVisible, onClose }: CorsWarningProps) {
  const [shouldRender, setShouldRender] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (isVisible) {
      setShouldRender(true)
      setTimeout(() => setIsAnimating(true), 10)
    } else {
      setIsAnimating(false)
      setTimeout(() => setShouldRender(false), 300)
    }
  }, [isVisible])

  const handleClose = () => {
    setIsAnimating(false)
    setTimeout(() => {
      setShouldRender(false)
      onClose()
    }, 300)
  }

  if (!shouldRender) return null

  return (
    <div 
      className={`fixed inset-0 z-50 flex items-center justify-center transition-all duration-300 ease-in-out ${
        isAnimating 
          ? 'bg-black/50 backdrop-blur-sm opacity-100' 
          : 'bg-black/0 backdrop-blur-none opacity-0'
      }`}
      onClick={handleClose}
    >
      <Card 
        className={`w-full max-w-md mx-4 border-red-200 dark:border-red-800 transition-all duration-300 ease-in-out transform ${
          isAnimating 
            ? 'opacity-100 scale-100 translate-y-0' 
            : 'opacity-0 scale-95 translate-y-4'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <CardTitle className="text-red-700 dark:text-red-400">
                CORS Error Detected
              </CardTitle>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription className="text-red-600 dark:text-red-300">
            API access blocked by browser security policy
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950">
            <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            <AlertDescription className="text-amber-800 dark:text-amber-200">
              <strong>Developer Action Required:</strong> The Luarmor API is blocking your requests due to CORS policy.
            </AlertDescription>
          </Alert>
          
          <div className="space-y-3 text-sm">
            <div>
              <h4 className="font-semibold text-foreground mb-2">Required Actions:</h4>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                <li>Contact Luarmor support to whitelist your server IP</li>
                <li>Add your domain to the allowed origins list</li>
                <li>Ensure your API key has proper permissions</li>
              </ul>
            </div>
            
            <div className="p-3 bg-muted rounded-md">
              <p className="text-xs font-mono">
                Current Origin: <span className="text-blue-600 dark:text-blue-400">{window.location.origin}</span>
              </p>
            </div>
          </div>

          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => window.open('https://luarmor.net/contact', '_blank')}
              className="flex-1"
            >
              Contact Support
            </Button>
            <Button 
              size="sm" 
              onClick={handleClose}
              className="flex-1"
            >
              Dismiss
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 
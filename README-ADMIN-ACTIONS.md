# Admin Action System

This system allows administrators to take actions against connected users in real-time.

## Features

### ```Admin Actions```
- **🚫 Kick**: Remove user from game with reason
- **⛔ Ban**: Temporary or permanent bans with duration
- **📢 Announcement**: Send messages to users

### ```Key Capabilities```

**Real Roblox Integration**
- Users are actually kicked from the Roblox game
- Professional kick messages with reasons
- Immediate enforcement

**Ban System**
- Multiple duration options (1 hour to permanent)
- Automatic blocking on reconnection attempts
- Smart time calculations and messaging

**Disconnect Tracking**
- Distinguishes admin actions from natural disconnects
- Logs all disconnection reasons
- Tracks pending actions per connection

## Usage

### ```Web Interface```
1. Navigate to **Connections** page
2. Click the **⋯** menu next to any user
3. Select desired action
4. Fill in required details
5. Execute action

### ```API Endpoints```

**Admin Actions**: `POST /api/admin/actions`
```json
{
  "action": "kick|ban|announcement",
  "connectionId": "string",
  "userId": "string", 
  "data": {
    "reason": "string",
    "message": "string",
    "duration": "1h|1d|1w|permanent",
    "expiresAt": 1234567890
  }
}
```

**Check Bans**: `GET /api/admin/bans?userId=123`

## System Flow

### ```Kick Process```
1. Admin clicks "Kick User"
2. System stores pending kick action
3. Next heartbeat delivers kick command
4. Client receives kick and displays message
5. User is kicked from Roblox with reason
6. Connection is closed with admin reason logged

### ```Ban Process```
1. Admin clicks "Ban User" 
2. Ban data saved to `/data/bans.json`
3. Pending ban action stored
4. Next heartbeat delivers ban command
5. User kicked from Roblox with ban message
6. Future connection attempts blocked

### ```Announcement Process```
1. Admin sends announcement
2. Pending announcement stored
3. Next heartbeat delivers message
4. User sees announcement in-game
5. No disconnection occurs

## Technical Details

### ```Pending Actions```
- Stored in memory for fast access
- Auto-cleanup after 30 seconds
- Tracks execution status

### ```Disconnection Logging```
```
🔧 Admin-initiated disconnect: connectionId (kick: reason)
🔌 Natural disconnect: connectionId (player left or network issue)
```

### ```Ban Storage```
```json
{
  "userId": "string",
  "reason": "string", 
  "bannedAt": 1234567890,
  "expiresAt": 1234567890,
  "bannedBy": "Admin",
  "duration": "1d",
  "connectionId": "string"
}
```

## Client Integration

The Lua client automatically handles all admin actions:

- **Kicks**: Shows warning and kicks from Roblox
- **Bans**: Shows ban message and kicks from Roblox  
- **Announcements**: Displays admin messages
- **Reconnection**: Checks ban status automatically

## Security

- All admin actions require valid connection IDs
- Ban data persists across server restarts
- CORS protection on all endpoints
- Input validation and sanitization 
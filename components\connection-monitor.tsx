'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Activity, Users, Zap, TrendingUp, Globe, Cpu, RefreshCw, AlertCircle, MoreHorizontal, UserX, Ban, MessageSquare } from 'lucide-react'
import { useWebSocket } from '@/lib/websocket-store'
import { AdminActionDialog } from '@/components/admin-action-dialog'
import * as React from 'react'

export function ConnectionMonitor() {
  const { connected, activeConnections, recentExecutions, totalExecutionsToday, peakConcurrent, loading, error, refreshData } = useWebSocket()
  const [selectedConnection, setSelectedConnection] = React.useState<any>(null)
  const [actionType, setActionType] = React.useState<'kick' | 'ban' | 'announcement' | null>(null)
  const [dialogOpen, setDialogOpen] = React.useState(false)

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString()
  }

  const getTimeSince = (timestamp: number) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000)
    if (seconds < 60) return `${seconds}s ago`
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`
    return `${Math.floor(seconds / 3600)}h ago`
  }

  const handleAdminAction = (connection: any, action: 'kick' | 'ban' | 'announcement') => {
    setSelectedConnection(connection)
    setActionType(action)
    setDialogOpen(true)
  }

  const handleActionSubmit = async (action: string, data: any) => {
    if (!selectedConnection) return

    try {
      const response = await fetch('/api/admin/actions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action,
          connectionId: selectedConnection.id,
          userId: selectedConnection.userId,
          data
        })
      })

      const result = await response.json()
      
      if (result.success) {
        console.log(`[Admin] ${action} action successful:`, result)
        
        // Show success message
        const actionName = action.charAt(0).toUpperCase() + action.slice(1)
        alert(`${actionName} action completed successfully!`)
        
        // Refresh connections to update the display
        await refreshData()
      } else {
        throw new Error(result.error || 'Action failed')
      }
    } catch (error) {
      console.error('[Admin] Action failed:', error)
      throw error
    }
  }

  return (
    <div className="space-y-4">
      {error && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Connection Error: {error}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refreshData()}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-sm text-muted-foreground">
            Last updated: {new Date().toLocaleTimeString()}
          </h2>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => refreshData()}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>
      
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Live Connections</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeConnections.length}</div>
            <p className="text-xs text-muted-foreground">
              Currently connected
            </p>
            {connected ? (
              <Badge className="mt-2" variant="default">
                <Activity className="mr-1 h-3 w-3" />
                Live
              </Badge>
            ) : (
              <Badge className="mt-2" variant="secondary">
                Offline
              </Badge>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Executions Today</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalExecutionsToday.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Total script runs
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Peak Concurrent</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{peakConcurrent}</div>
            <p className="text-xs text-muted-foreground">
              Maximum simultaneous
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Session</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12m</div>
            <p className="text-xs text-muted-foreground">
              Average duration
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Ping</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {activeConnections.length > 0 
                ? Math.round(activeConnections.reduce((sum, conn) => sum + (conn.ping?.current || 0), 0) / activeConnections.length) 
                : 0}ms
            </div>
            <p className="text-xs text-muted-foreground">
              Network latency
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Active Connections</CardTitle>
          <CardDescription>
            Real-time view of connected users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User ID</TableHead>
                  <TableHead>Game</TableHead>
                  <TableHead>Executor</TableHead>
                  <TableHead>Connected</TableHead>
                  <TableHead>Last Ping</TableHead>
                  <TableHead>Ping</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {activeConnections.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                      No active connections
                    </TableCell>
                  </TableRow>
                ) : (
                  activeConnections.map((connection) => (
                    <TableRow key={connection.id}>
                      <TableCell className="font-mono text-sm">
                        {connection.userId.substring(0, 8)}...
                      </TableCell>
                      <TableCell>
                        {connection.gameInfo ? (
                          <div className="flex items-center gap-2">
                            <Globe className="h-3 w-3" />
                            <span className="text-sm">{connection.gameInfo.placeName}</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Unknown</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {connection.executorInfo ? (
                          <div className="flex items-center gap-2">
                            <Cpu className="h-3 w-3" />
                            <span className="text-sm">{connection.executorInfo.name}</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Unknown</span>
                        )}
                      </TableCell>
                      <TableCell>{formatTime(connection.connectedAt)}</TableCell>
                      <TableCell>{getTimeSince(connection.lastPing)}</TableCell>
                      <TableCell>
                        {connection.ping ? (
                          <div className="text-sm">
                            <div className="font-medium">{connection.ping.current}ms</div>
                            <div className="text-xs text-muted-foreground">
                              avg: {connection.ping.average}ms
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">N/A</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={connection.status === 'active' ? 'default' : 'secondary'} 
                          className="text-xs"
                        >
                          {connection.status === 'active' ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem 
                              onClick={() => handleAdminAction(connection, 'kick')}
                              className="text-orange-600 hover:text-orange-700"
                            >
                              <UserX className="mr-2 h-4 w-4" />
                              Kick User
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleAdminAction(connection, 'ban')}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Ban className="mr-2 h-4 w-4" />
                              Ban User
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleAdminAction(connection, 'announcement')}
                              className="text-blue-600 hover:text-blue-700"
                            >
                              <MessageSquare className="mr-2 h-4 w-4" />
                              Send Announcement
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Executions</CardTitle>
          <CardDescription>
            Last 100 script executions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {recentExecutions.slice(0, 10).map((execution, index) => (
              <div key={index} className="flex items-center justify-between p-2 border rounded">
                <div className="flex items-center gap-3">
                  <Badge variant="outline">{execution.scriptId}</Badge>
                  <span className="text-sm font-mono">{execution.userId.substring(0, 8)}...</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  {execution.placeName && (
                    <span className="flex items-center gap-1">
                      <Globe className="h-3 w-3" />
                      {execution.placeName}
                    </span>
                  )}
                  <span>{getTimeSince(execution.timestamp)}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Admin Action Dialog */}
      {selectedConnection && (
        <AdminActionDialog
          connection={selectedConnection}
          action={actionType}
          open={dialogOpen}
          onClose={() => {
            setDialogOpen(false)
            setSelectedConnection(null)
            setActionType(null)
          }}
          onSubmit={handleActionSubmit}
        />
      )}
    </div>
  )
} 
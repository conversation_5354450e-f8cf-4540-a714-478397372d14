import { NextRequest, NextResponse } from 'next/server'

interface ConnectionRequest {
  userId: string
  scriptId: string
  hwid: string
  discordId?: string
  gameInfo?: {
    gameId: string
    placeName: string
    placeId: string
  }
  executorInfo?: {
    name: string
    version: string
  }
}

const activeConnections = new Map<string, any>()

// Export for use in other modules
export { activeConnections }

// Add CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
}

export async function OPTIONS(request: NextRequest) {
  return new Response(null, { status: 200, headers: corsHeaders })
}

export async function POST(request: NextRequest) {
  try {
    const body: ConnectionRequest = await request.json()
    
    console.log('[Luarmor] Connection request received:', body)
    
    if (!body.userId || !body.scriptId || !body.hwid) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, scriptId, hwid' },
        { status: 400, headers: corsHeaders }
      )
    }

    // Check if user is banned
    try {
      const { checkUserBan } = await import('../admin/actions/route')
      const banData = await checkUserBan(body.userId)
      
      if (banData) {
        const timeRemaining = banData.expiresAt ? banData.expiresAt - Date.now() : null
        const isExpired = timeRemaining !== null && timeRemaining <= 0
        
        if (!isExpired) {
          console.log('[Luarmor] Banned user attempted connection:', body.userId)
          
          let timeMessage = 'permanently'
          if (timeRemaining && timeRemaining > 0) {
            const days = Math.floor(timeRemaining / (24 * 60 * 60 * 1000))
            const hours = Math.floor((timeRemaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
            const minutes = Math.floor((timeRemaining % (60 * 60 * 1000)) / (60 * 1000))
            
            if (days > 0) {
              timeMessage = `in ${days} day${days > 1 ? 's' : ''} and ${hours} hour${hours > 1 ? 's' : ''}`
            } else if (hours > 0) {
              timeMessage = `in ${hours} hour${hours > 1 ? 's' : ''} and ${minutes} minute${minutes > 1 ? 's' : ''}`
            } else {
              timeMessage = `in ${minutes} minute${minutes > 1 ? 's' : ''}`
            }
          }
          
          return NextResponse.json({
            error: 'USER_BANNED',
            message: `[Pulse Hub Administrator] You have been banned from this service for: ${banData.reason}. You will be unbanned ${timeMessage}.`,
            banData: {
              reason: banData.reason,
              bannedAt: banData.bannedAt,
              expiresAt: banData.expiresAt,
              timeRemaining: timeRemaining
            }
          }, { status: 403, headers: corsHeaders })
        }
      }
    } catch (banCheckError) {
      console.error('[Luarmor] Ban check error:', banCheckError)
      // Continue with connection if ban check fails
    }

    const connectionId = `${body.userId}-${Date.now()}`
    const connectionData = {
      id: connectionId,
      userId: body.userId,
      scriptId: body.scriptId,
      hwid: body.hwid,
      discordId: body.discordId,
      connectedAt: Date.now(),
      lastPing: Date.now(),
      gameInfo: body.gameInfo,
      executorInfo: body.executorInfo,
      status: 'active',
      ping: {
        current: 0,
        average: 0,
        min: 0,
        max: 0,
        history: []
      }
    }

    activeConnections.set(connectionId, connectionData)

    console.log('[Luarmor] Connection established:', connectionId)

    // TODO: Emit to WebSocket clients when server is integrated
    
    return NextResponse.json({
      success: true,
      connectionId,
      message: 'Connection established',
      data: connectionData
    }, { status: 201, headers: corsHeaders })
  } catch (error) {
    console.error('[Luarmor] Connection error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    )
  }
}

export async function GET(request: NextRequest) {
  // Clean up stale connections (older than 2 minutes without heartbeat)
  const now = Date.now()
  const staleThreshold = 2 * 60 * 1000 // 2 minutes
  
  for (const [id, connection] of activeConnections.entries()) {
    if (now - connection.lastPing > staleThreshold) {
      console.log('[API] Removing stale connection:', id)
      activeConnections.delete(id)
    }
  }
  
  const connections = Array.from(activeConnections.values())
  
  console.log('[API] GET /api/connections - returning', connections.length, 'connections')
  
  return NextResponse.json({
    success: true,
    connections,
    total: connections.length,
    active: connections.filter(c => c.status === 'active').length
  }, { headers: corsHeaders })
}

export async function DELETE(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const connectionId = searchParams.get('connectionId')
  
  if (!connectionId) {
    return NextResponse.json(
      { error: 'Connection ID required' },
      { status: 400, headers: corsHeaders }
    )
  }

  if (activeConnections.has(connectionId)) {
    // Check if this was an admin-initiated disconnect
    try {
      const { wasAdminDisconnect } = await import('../admin/actions/route')
      const adminAction = wasAdminDisconnect(connectionId)
      
      if (adminAction) {
        console.log(`[Luarmor] 🔧 Admin-initiated disconnect: ${connectionId} (${adminAction.action}: ${adminAction.reason || adminAction.message})`)
      } else {
        console.log(`[Luarmor] 🔌 Natural disconnect: ${connectionId} (player left or network issue)`)
      }
    } catch (error) {
      console.log(`[Luarmor] 🔌 Natural disconnect: ${connectionId} (could not check admin actions)`)
    }
    
    activeConnections.delete(connectionId)
    console.log('[Luarmor] Connection removed:', connectionId)
    return NextResponse.json({
      success: true,
      message: 'Connection closed'
    }, { headers: corsHeaders })
  }

  return NextResponse.json(
    { error: 'Connection not found' },
    { status: 404, headers: corsHeaders }
  )
} 
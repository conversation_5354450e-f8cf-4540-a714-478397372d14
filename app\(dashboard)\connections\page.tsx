'use client'

import { ConnectionMonitor } from '@/components/connection-monitor'
import { BanManagement } from '@/components/ban-management'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { WebSocketProvider } from '@/lib/websocket-store'

export default function ConnectionsPage() {
  return (
    <WebSocketProvider>
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Connections</h2>
        </div>
        
        <Tabs defaultValue="monitor" className="space-y-4">
          <TabsList>
            <TabsTrigger value="monitor">Live Monitor</TabsTrigger>
            <TabsTrigger value="bans">Ban Management</TabsTrigger>
          </TabsList>
          
          <TabsContent value="monitor" className="space-y-4">
            <ConnectionMonitor />
          </TabsContent>
          
          <TabsContent value="bans" className="space-y-4">
            <BanManagement />
          </TabsContent>
        </Tabs>
      </div>
    </WebSocketProvider>
  )
}

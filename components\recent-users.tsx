import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { UserPlus, Clock } from "lucide-react"

export function RecentUsers() {
  return (
    <div className="space-y-8">
      {recentUsers.map((user) => (
        <div key={user.id} className="flex items-center">
          <Avatar className="h-9 w-9 border">
            <AvatarFallback className="bg-primary/10 text-primary">
              {user.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>
          <div className="ml-4 space-y-1">
            <p className="text-sm font-medium leading-none">{user.name}</p>
            <p className="text-sm text-muted-foreground flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {user.registeredAt}
            </p>
          </div>
          <div className="ml-auto flex items-center gap-2">
            <div
              className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold ${
                user.status === "Active"
                  ? "bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300"
                  : "bg-yellow-50 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-300"
              }`}
            >
              {user.status}
            </div>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </div>
        </div>
      ))}
    </div>
  )
}

const recentUsers = [
  {
    id: "1",
    name: "Thabo Mbeki",
    registeredAt: "2 hours ago",
    status: "Active",
  },
  {
    id: "2",
    name: "Nomzamo Mbatha",
    registeredAt: "4 hours ago",
    status: "Active",
  },
  {
    id: "3",
    name: "Siya Kolisi",
    registeredAt: "6 hours ago",
    status: "Active",
  },
  {
    id: "4",
    name: "Trevor Noah",
    registeredAt: "8 hours ago",
    status: "Pending",
  },
  {
    id: "5",
    name: "Patrice Motsepe",
    registeredAt: "12 hours ago",
    status: "Active",
  },
]

'use client'

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { UsersIcon, ActivityIcon, TrendingUpIcon, ClockIcon } from "lucide-react"
import { Analytics<PERSON>hart } from "@/components/analytics-chart"
import { UserActivityChart } from "@/components/user-activity-chart"
import { LuarmorSetup } from "@/components/luarmor-setup"
import { LuarmorStatsCards } from "@/components/luarmor-stats"
import { LuarmorExecutionChart } from "@/components/luarmor-execution-chart"
import { useLuarmor } from "@/lib/luarmor-store"

export default function AnalyticsPage() {
  const { apiKey, keyDetails, keyStats, users } = useLuarmor()

  if (!apiKey || !keyDetails) {
    return (
      <div className="flex flex-col gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Analytics</h1>
          <p className="text-muted-foreground">Connect your Luarmor API to view analytics.</p>
        </div>
        <div className="flex items-center justify-center min-h-[400px]">
          <LuarmorSetup />
        </div>
      </div>
    )
  }

  const activeUsers = users.filter(user => user.status === 'active').length
  const bannedUsers = users.filter(user => user.banned === 1).length
  const totalExecutions = users.reduce((sum, user) => sum + user.total_executions, 0)

  return (
    <div className="flex flex-col gap-4">
      <div>
        <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Analytics</h1>
        <p className="text-muted-foreground">
          Analytics and insights for {keyDetails.projects[0]?.name || 'your project'}
        </p>
      </div>

      <LuarmorStatsCards />

      <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
        <LuarmorExecutionChart />
        
        <Card>
          <CardHeader>
            <CardTitle>User Breakdown</CardTitle>
            <CardDescription>
              User status distribution and activity
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{activeUsers}</div>
                <div className="text-sm text-muted-foreground">Active Users</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{bannedUsers}</div>
                <div className="text-sm text-muted-foreground">Banned Users</div>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Active Rate</span>
                <span className="text-sm font-medium">
                  {users.length > 0 ? Math.round((activeUsers / users.length) * 100) : 0}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full" 
                  style={{ width: `${users.length > 0 ? (activeUsers / users.length) * 100 : 0}%` }}
                ></div>
              </div>
            </div>

            <div className="pt-4 border-t">
              <div className="text-center">
                <div className="text-2xl font-bold">{totalExecutions.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Total Executions</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {keyStats && (
        <Card>
          <CardHeader>
            <CardTitle>Resource Usage</CardTitle>
            <CardDescription>
              Your Luarmor resource usage and limits
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Users</span>
                  <span className="text-sm text-muted-foreground">
                    {keyStats.stats.users} / {keyStats.stats.default.users}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${(keyStats.stats.users / keyStats.stats.default.users) * 100}%` }}
                  ></div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Scripts</span>
                  <span className="text-sm text-muted-foreground">
                    {keyStats.stats.scripts} / {keyStats.stats.default.scripts}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-purple-600 h-2 rounded-full" 
                    style={{ width: `${(keyStats.stats.scripts / keyStats.stats.default.scripts) * 100}%` }}
                  ></div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Obfuscations</span>
                  <span className="text-sm text-muted-foreground">
                    {keyStats.stats.obfuscations} / {keyStats.stats.default.obfuscations}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-orange-600 h-2 rounded-full" 
                    style={{ width: `${(keyStats.stats.obfuscations / keyStats.stats.default.obfuscations) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

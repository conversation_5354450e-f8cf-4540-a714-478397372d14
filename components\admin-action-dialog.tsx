'use client'

import * as React from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON>alogFooter, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { AlertTriangle, Ban, MessageSquare, UserX } from 'lucide-react'

interface ConnectionData {
  id: string
  userId: string
  scriptId: string
  hwid: string
  connectedAt: number
  lastPing: number
  status: string
  gameInfo?: {
    gameId: string
    placeName: string
    placeId: string
  }
  executorInfo?: {
    name: string
    version: string
  }
}

interface AdminActionDialogProps {
  connection: ConnectionData
  action: 'kick' | 'ban' | 'announcement' | null
  open: boolean
  onClose: () => void
  onSubmit: (action: string, data: any) => Promise<void>
}

const banDurations = [
  { value: '1h', label: '1 Hour', seconds: 3600 },
  { value: '6h', label: '6 Hours', seconds: 21600 },
  { value: '12h', label: '12 Hours', seconds: 43200 },
  { value: '1d', label: '1 Day', seconds: 86400 },
  { value: '3d', label: '3 Days', seconds: 259200 },
  { value: '1w', label: '1 Week', seconds: 604800 },
  { value: '2w', label: '2 Weeks', seconds: 1209600 },
  { value: '1m', label: '1 Month', seconds: 2592000 },
  { value: '3m', label: '3 Months', seconds: 7776000 },
  { value: '6m', label: '6 Months', seconds: 15552000 },
  { value: '1y', label: '1 Year', seconds: 31536000 },
  { value: 'permanent', label: 'Permanent', seconds: -1 }
]

export function AdminActionDialog({ connection, action, open, onClose, onSubmit }: AdminActionDialogProps) {
  const [reason, setReason] = React.useState('')
  const [duration, setDuration] = React.useState('1d')
  const [message, setMessage] = React.useState('')
  const [loading, setLoading] = React.useState(false)

  React.useEffect(() => {
    if (!open) {
      setReason('')
      setDuration('1d')
      setMessage('')
      setLoading(false)
    }
  }, [open])

  const handleSubmit = async () => {
    if (!action) return

    let data: any = { reason }

    switch (action) {
      case 'kick':
        data = { reason }
        break
      case 'ban':
        if (!reason.trim()) {
          alert('Please provide a reason for the ban')
          return
        }
        const selectedDuration = banDurations.find(d => d.value === duration)
        data = {
          reason,
          duration: duration,
          durationSeconds: selectedDuration?.seconds || 86400,
          expiresAt: selectedDuration?.seconds === -1 ? null : Date.now() + (selectedDuration?.seconds || 86400) * 1000
        }
        break
      case 'announcement':
        if (!message.trim()) {
          alert('Please provide an announcement message')
          return
        }
        data = { message }
        break
    }

    try {
      setLoading(true)
      await onSubmit(action, data)
      onClose()
    } catch (error) {
      console.error('Admin action failed:', error)
      alert('Action failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const getActionInfo = () => {
    switch (action) {
      case 'kick':
        return {
          icon: <UserX className="h-5 w-5" />,
          title: 'Kick User',
          description: 'Remove the user from the game session',
          color: 'text-orange-600',
          bgColor: 'bg-orange-50 dark:bg-orange-900/20'
        }
      case 'ban':
        return {
          icon: <Ban className="h-5 w-5" />,
          title: 'Ban User',
          description: 'Permanently restrict user access',
          color: 'text-red-600',
          bgColor: 'bg-red-50 dark:bg-red-900/20'
        }
      case 'announcement':
        return {
          icon: <MessageSquare className="h-5 w-5" />,
          title: 'Send Announcement',
          description: 'Send a message to the user',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20'
        }
      default:
        return {
          icon: <AlertTriangle className="h-5 w-5" />,
          title: 'Admin Action',
          description: 'Perform administrative action',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50 dark:bg-gray-900/20'
        }
    }
  }

  const actionInfo = getActionInfo()

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${actionInfo.bgColor}`}>
              <span className={actionInfo.color}>{actionInfo.icon}</span>
            </div>
            {actionInfo.title}
          </DialogTitle>
          <DialogDescription>
            {actionInfo.description}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* User Info */}
          <div className={`p-3 rounded-lg ${actionInfo.bgColor} border`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">{connection.userId}</p>
                <p className="text-sm text-muted-foreground">
                  {connection.gameInfo?.placeName || 'Unknown Game'}
                </p>
              </div>
              <Badge variant="outline">
                {connection.executorInfo?.name || 'Unknown'}
              </Badge>
            </div>
          </div>

          {/* Action-specific inputs */}
          {(action === 'kick' || action === 'ban') && (
            <div className="space-y-2">
              <Label htmlFor="reason">Reason *</Label>
              <Textarea
                id="reason"
                placeholder="Enter the reason for this action..."
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="min-h-[80px]"
              />
            </div>
          )}

          {action === 'ban' && (
            <div className="space-y-2">
              <Label htmlFor="duration">Ban Duration</Label>
              <Select value={duration} onValueChange={setDuration}>
                <SelectTrigger>
                  <SelectValue placeholder="Select duration" />
                </SelectTrigger>
                <SelectContent>
                  {banDurations.map((dur) => (
                    <SelectItem key={dur.value} value={dur.value}>
                      {dur.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {action === 'announcement' && (
            <div className="space-y-2">
              <Label htmlFor="message">Announcement Message *</Label>
              <Textarea
                id="message"
                placeholder="Enter your announcement message..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          )}
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading}
            className={
              action === 'ban' 
                ? 'bg-red-600 hover:bg-red-700' 
                : action === 'kick'
                ? 'bg-orange-600 hover:bg-orange-700'
                : 'bg-blue-600 hover:bg-blue-700'
            }
          >
            {loading ? 'Processing...' : `${actionInfo.title.split(' ')[0]} User`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 
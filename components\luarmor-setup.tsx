'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Shield, AlertCircle, Code } from 'lucide-react'
import { useLuarmor } from '@/lib/luarmor-store'
import { CorsWarning } from '@/components/cors-warning'

export function LuarmorSetup() {
  const { apiKey, keyDetails, selectedProject, setSelectedProject, loading, error, corsError, setCorsError } = useLuarmor()

  if (!apiKey) {
    return (
      <>
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-destructive/10 rounded-lg flex items-center justify-center mb-4">
              <AlertCircle className="w-6 h-6 text-destructive" />
            </div>
            <CardTitle>Luarmor API Not Configured</CardTitle>
            <CardDescription>
              Please configure your Luarmor API key to access the dashboard
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <Code className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p className="font-medium">To configure your API key:</p>
                  <ol className="list-decimal list-inside space-y-1 text-sm">
                    <li>Create a <code className="px-1 py-0.5 bg-muted rounded">.env.local</code> file in your project root</li>
                    <li>Add your API key: <code className="px-1 py-0.5 bg-muted rounded">NEXT_PUBLIC_LUARMOR_API_KEY=your_key_here</code></li>
                    <li>Restart your development server</li>
                  </ol>
                </div>
              </AlertDescription>
            </Alert>
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
        <CorsWarning isVisible={corsError} onClose={() => setCorsError(false)} />
      </>
    )
  }

  if (keyDetails && keyDetails.projects.length > 1) {
    return (
      <>
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Select Project</CardTitle>
            <CardDescription>
              Choose which project you want to manage
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="project">Project</Label>
              <Select value={selectedProject || ''} onValueChange={setSelectedProject}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                              {keyDetails.projects.map((project: any) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.name}
                </SelectItem>
              ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
        <CorsWarning isVisible={corsError} onClose={() => setCorsError(false)} />
      </>
    )
  }

  return (
    <>
      <CorsWarning isVisible={corsError} onClose={() => setCorsError(false)} />
    </>
  )
} 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smooth 3D Carousel Test</title>
    <style>
        body {
            margin: 0;
            padding: 50px;
            background: linear-gradient(135deg, #1e293b, #334155, #475569);
            font-family: Arial, sans-serif;
            color: white;
            overflow-x: hidden;
        }
        
        .carousel-container {
            perspective: 2000px;
            perspective-origin: center center;
            position: relative;
            margin: 50px auto;
            width: 100%;
            max-width: 1200px;
        }
        
        .carousel-track {
            transform-style: preserve-3d;
            position: relative;
            height: 520px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .card-container {
            position: absolute;
            transform-style: preserve-3d;
        }
        
        .card {
            width: 320px;
            height: 480px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2));
            border: 1px solid rgba(59, 130, 246, 0.4);
            border-radius: 24px;
            backdrop-filter: blur(8px);
            transform-style: preserve-3d;
            backface-visibility: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            will-change: transform, opacity;
        }
        
        /* Smooth 3D positioning classes with transitions */
        .card-position-center {
            transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1) !important;
            z-index: 30;
            opacity: 1;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
            transform-style: preserve-3d;
            will-change: transform, opacity;
        }

        .card-position-left {
            transform: translateX(-320px) translateZ(0px) rotateY(-15deg) scale(0.9) !important;
            z-index: 20;
            opacity: 1;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
            transform-style: preserve-3d;
            will-change: transform, opacity;
        }

        .card-position-right {
            transform: translateX(320px) translateZ(0px) rotateY(15deg) scale(0.9) !important;
            z-index: 20;
            opacity: 1;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
            transform-style: preserve-3d;
            will-change: transform, opacity;
        }

        .card-position-far-left {
            transform: translateX(-640px) translateZ(-100px) rotateY(-25deg) scale(0.7) !important;
            z-index: 10;
            opacity: 0.4;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
            transform-style: preserve-3d;
            will-change: transform, opacity;
        }

        .card-position-far-right {
            transform: translateX(640px) translateZ(-100px) rotateY(25deg) scale(0.7) !important;
            z-index: 10;
            opacity: 0.4;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
            transform-style: preserve-3d;
            will-change: transform, opacity;
        }

        .card-position-hidden-left {
            transform: translateX(-960px) translateZ(-200px) rotateY(-35deg) scale(0.5) !important;
            z-index: 5;
            opacity: 0;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
            transform-style: preserve-3d;
            will-change: transform, opacity;
        }

        .card-position-hidden-right {
            transform: translateX(960px) translateZ(-200px) rotateY(35deg) scale(0.5) !important;
            z-index: 5;
            opacity: 0;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
            transform-style: preserve-3d;
            will-change: transform, opacity;
        }
        
        /* Hover effects */
        .card-position-center:hover .card {
            transform: translateZ(20px) scale(1.05);
        }
        
        .controls {
            text-align: center;
            margin-top: 30px;
        }
        
        button {
            background: rgba(59, 130, 246, 0.8);
            border: none;
            color: white;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: rgba(59, 130, 246, 1);
            transform: scale(1.05);
        }
        
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .info {
            text-align: center;
            margin-top: 20px;
            color: #94a3b8;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; margin-bottom: 50px;">Smooth 3D Carousel Transition Test</h1>
    
    <div class="carousel-container">
        <div class="carousel-track">
            <div class="card-container card-position-hidden-left" id="card0">
                <div class="card">Card 1</div>
            </div>
            <div class="card-container card-position-far-left" id="card1">
                <div class="card">Card 2</div>
            </div>
            <div class="card-container card-position-left" id="card2">
                <div class="card">Card 3</div>
            </div>
            <div class="card-container card-position-center" id="card3">
                <div class="card">Card 4</div>
            </div>
            <div class="card-container card-position-right" id="card4">
                <div class="card">Card 5</div>
            </div>
            <div class="card-container card-position-far-right" id="card5">
                <div class="card">Card 6</div>
            </div>
            <div class="card-container card-position-hidden-right" id="card6">
                <div class="card">Card 7</div>
            </div>
        </div>
    </div>
    
    <div class="controls">
        <button onclick="prevCard()" id="prevBtn">← Previous</button>
        <button onclick="nextCard()" id="nextBtn">Next →</button>
    </div>
    
    <div class="info">
        <p>Current center card: <span id="currentCard">Card 4</span></p>
        <p>Watch the smooth sliding transitions with 3D rotation and scaling!</p>
    </div>
    
    <script>
        let currentIndex = 3; // Start with card 4 in center
        const totalCards = 7;
        let isTransitioning = false;
        
        const positions = [
            'card-position-hidden-left',
            'card-position-far-left', 
            'card-position-left',
            'card-position-center',
            'card-position-right',
            'card-position-far-right',
            'card-position-hidden-right'
        ];
        
        function updateCardPositions() {
            for (let i = 0; i < totalCards; i++) {
                const card = document.getElementById(`card${i}`);
                const relativePosition = i - currentIndex + 3; // Center position is index 3
                
                // Remove all position classes
                card.className = 'card-container';
                
                // Add appropriate position class
                if (relativePosition >= 0 && relativePosition < positions.length) {
                    card.classList.add(positions[relativePosition]);
                } else if (relativePosition < 0) {
                    card.classList.add('card-position-hidden-left');
                } else {
                    card.classList.add('card-position-hidden-right');
                }
            }
            
            // Update UI
            document.getElementById('currentCard').textContent = `Card ${currentIndex + 1}`;
        }
        
        function nextCard() {
            if (isTransitioning) return;
            isTransitioning = true;
            
            currentIndex = (currentIndex + 1) % totalCards;
            updateCardPositions();
            
            setTimeout(() => {
                isTransitioning = false;
            }, 800);
        }
        
        function prevCard() {
            if (isTransitioning) return;
            isTransitioning = true;
            
            currentIndex = (currentIndex - 1 + totalCards) % totalCards;
            updateCardPositions();
            
            setTimeout(() => {
                isTransitioning = false;
            }, 800);
        }
        
        // Initialize
        updateCardPositions();
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                prevCard();
            } else if (e.key === 'ArrowRight') {
                e.preventDefault();
                nextCard();
            }
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Rolling Animation Test</title>
    <style>
        body {
            margin: 0;
            padding: 50px;
            background: linear-gradient(135deg, #1e293b, #334155, #475569);
            font-family: Arial, sans-serif;
            color: white;
            overflow-x: hidden;
        }
        
        .carousel-container {
            perspective: 2000px;
            perspective-origin: center center;
            position: relative;
            margin: 50px auto;
            width: 100%;
            max-width: 1200px;
        }
        
        .carousel-track {
            transform-style: preserve-3d;
            position: relative;
            height: 520px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .card-container {
            position: absolute;
            transform-style: preserve-3d;
        }
        
        .card {
            width: 320px;
            height: 480px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2));
            border: 1px solid rgba(59, 130, 246, 0.4);
            border-radius: 24px;
            backdrop-filter: blur(8px);
            transform-style: preserve-3d;
            backface-visibility: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        /* Static positioning classes */
        .card-center {
            transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1);
            z-index: 30;
        }
        
        .card-left {
            transform: translateX(-320px) translateZ(0px) rotateY(-15deg) scale(0.9);
            z-index: 20;
        }
        
        .card-right {
            transform: translateX(320px) translateZ(0px) rotateY(15deg) scale(0.9);
            z-index: 20;
        }
        
        /* 3D Rolling Animations */
        .cylinder-rolling-right {
            animation: cylinder-roll-right 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
        
        .cylinder-rolling-left {
            animation: cylinder-roll-left 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
        
        .cylinder-entering-left {
            animation: cylinder-enter-from-left 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
        
        .cylinder-entering-right {
            animation: cylinder-enter-from-right 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
        
        @keyframes cylinder-roll-right {
            0% {
                transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1);
                opacity: 1;
            }
            20% {
                transform: translateX(64px) translateZ(80px) rotateY(30deg) scale(1.08);
                opacity: 0.9;
            }
            40% {
                transform: translateX(128px) translateZ(20px) rotateY(60deg) scale(1.05);
                opacity: 0.7;
            }
            50% {
                transform: translateX(160px) translateZ(-80px) rotateY(90deg) scale(1);
                opacity: 0.3;
            }
            60% {
                transform: translateX(192px) translateZ(20px) rotateY(120deg) scale(0.95);
                opacity: 0.7;
            }
            80% {
                transform: translateX(256px) translateZ(50px) rotateY(150deg) scale(0.92);
                opacity: 0.9;
            }
            100% {
                transform: translateX(320px) translateZ(0px) rotateY(15deg) scale(0.9);
                opacity: 1;
            }
        }
        
        @keyframes cylinder-roll-left {
            0% {
                transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1);
                opacity: 1;
            }
            20% {
                transform: translateX(-64px) translateZ(80px) rotateY(-30deg) scale(1.08);
                opacity: 0.9;
            }
            40% {
                transform: translateX(-128px) translateZ(20px) rotateY(-60deg) scale(1.05);
                opacity: 0.7;
            }
            50% {
                transform: translateX(-160px) translateZ(-80px) rotateY(-90deg) scale(1);
                opacity: 0.3;
            }
            60% {
                transform: translateX(-192px) translateZ(20px) rotateY(-120deg) scale(0.95);
                opacity: 0.7;
            }
            80% {
                transform: translateX(-256px) translateZ(50px) rotateY(-150deg) scale(0.92);
                opacity: 0.9;
            }
            100% {
                transform: translateX(-320px) translateZ(0px) rotateY(-15deg) scale(0.9);
                opacity: 1;
            }
        }
        
        @keyframes cylinder-enter-from-left {
            0% {
                transform: translateX(-320px) translateZ(0px) rotateY(-15deg) scale(0.9);
                opacity: 1;
            }
            20% {
                transform: translateX(-256px) translateZ(50px) rotateY(-150deg) scale(0.92);
                opacity: 0.9;
            }
            40% {
                transform: translateX(-192px) translateZ(20px) rotateY(-120deg) scale(0.95);
                opacity: 0.7;
            }
            50% {
                transform: translateX(-160px) translateZ(-80px) rotateY(-90deg) scale(1);
                opacity: 0.3;
            }
            60% {
                transform: translateX(-128px) translateZ(20px) rotateY(-60deg) scale(1.05);
                opacity: 0.7;
            }
            80% {
                transform: translateX(-64px) translateZ(80px) rotateY(-30deg) scale(1.08);
                opacity: 0.9;
            }
            100% {
                transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1);
                opacity: 1;
            }
        }
        
        @keyframes cylinder-enter-from-right {
            0% {
                transform: translateX(320px) translateZ(0px) rotateY(15deg) scale(0.9);
                opacity: 1;
            }
            20% {
                transform: translateX(256px) translateZ(50px) rotateY(150deg) scale(0.92);
                opacity: 0.9;
            }
            40% {
                transform: translateX(192px) translateZ(20px) rotateY(120deg) scale(0.95);
                opacity: 0.7;
            }
            50% {
                transform: translateX(160px) translateZ(-80px) rotateY(90deg) scale(1);
                opacity: 0.3;
            }
            60% {
                transform: translateX(128px) translateZ(20px) rotateY(60deg) scale(1.05);
                opacity: 0.7;
            }
            80% {
                transform: translateX(64px) translateZ(80px) rotateY(30deg) scale(1.08);
                opacity: 0.9;
            }
            100% {
                transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1);
                opacity: 1;
            }
        }
        
        .controls {
            text-align: center;
            margin-top: 30px;
        }
        
        button {
            background: rgba(59, 130, 246, 0.8);
            border: none;
            color: white;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: rgba(59, 130, 246, 1);
        }
        
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; margin-bottom: 50px;">3D Cylindrical Rolling Animation Test</h1>
    
    <div class="carousel-container">
        <div class="carousel-track">
            <div class="card-container card-left" id="card1">
                <div class="card">Card 1</div>
            </div>
            <div class="card-container card-center" id="card2">
                <div class="card">Card 2</div>
            </div>
            <div class="card-container card-right" id="card3">
                <div class="card">Card 3</div>
            </div>
        </div>
    </div>
    
    <div class="controls">
        <button onclick="rollLeft()">← Roll Left</button>
        <button onclick="rollRight()">Roll Right →</button>
        <button onclick="resetCards()">Reset</button>
    </div>
    
    <script>
        let isAnimating = false;
        
        function rollRight() {
            if (isAnimating) return;
            isAnimating = true;
            
            const centerCard = document.querySelector('.card-center .card');
            const rightCard = document.querySelector('.card-right .card');
            
            if (centerCard) {
                centerCard.classList.add('cylinder-rolling-right');
            }
            if (rightCard) {
                rightCard.classList.add('cylinder-entering-left');
            }
            
            setTimeout(() => {
                isAnimating = false;
                // Clean up animation classes
                document.querySelectorAll('.card').forEach(card => {
                    card.classList.remove('cylinder-rolling-right', 'cylinder-rolling-left', 'cylinder-entering-left', 'cylinder-entering-right');
                });
            }, 1000);
        }
        
        function rollLeft() {
            if (isAnimating) return;
            isAnimating = true;
            
            const centerCard = document.querySelector('.card-center .card');
            const leftCard = document.querySelector('.card-left .card');
            
            if (centerCard) {
                centerCard.classList.add('cylinder-rolling-left');
            }
            if (leftCard) {
                leftCard.classList.add('cylinder-entering-right');
            }
            
            setTimeout(() => {
                isAnimating = false;
                // Clean up animation classes
                document.querySelectorAll('.card').forEach(card => {
                    card.classList.remove('cylinder-rolling-right', 'cylinder-rolling-left', 'cylinder-entering-left', 'cylinder-entering-right');
                });
            }, 1000);
        }
        
        function resetCards() {
            document.querySelectorAll('.card').forEach(card => {
                card.classList.remove('cylinder-rolling-right', 'cylinder-rolling-left', 'cylinder-entering-left', 'cylinder-entering-right');
            });
            isAnimating = false;
        }
    </script>
</body>
</html>

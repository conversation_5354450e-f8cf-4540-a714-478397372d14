'use client'

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Users, FileText, Shield, Activity, Zap, Calendar } from 'lucide-react'
import { useLuarmor } from '@/lib/luarmor-store'
import { CorsWarning } from '@/components/cors-warning'

export function LuarmorStatsCards() {
  const { keyStats, keyDetails, users, corsError, setCorsError } = useLuarmor()

  if (!keyStats || !keyDetails) {
    return null
  }

  const activeUsers = users.filter(user => user.status === 'active').length
  const bannedUsers = users.filter(user => user.banned === 1).length
  const totalExecutions = users.reduce((sum, user) => sum + user.total_executions, 0)
  const last31DaysExecutions = keyStats.execution_data.executions.reduce((sum, val) => sum + val, 0)

  const cards = [
    {
      title: 'Active Users',
      value: activeUsers,
      description: `${users.length} total users`,
      icon: Users,
      trend: users.length > 0 ? `${Math.round((activeUsers / users.length) * 100)}% active` : null
    },
    {
      title: 'Scripts',
      value: keyStats.stats.scripts,
      description: `${keyStats.stats.default.scripts} max scripts`,
      icon: FileText,
      trend: keyStats.stats.scripts > 0 ? 'Available' : 'None'
    },
    {
      title: 'Executions (31 days)',
      value: last31DaysExecutions,
      description: `${totalExecutions.toLocaleString()} all-time`,
      icon: Activity,
      trend: `${keyStats.execution_data.executions[keyStats.execution_data.executions.length - 1] || 0} today`
    },
    {
      title: 'Obfuscations',
      value: keyStats.stats.obfuscations,
      description: `${keyStats.stats.default.obfuscations} available`,
      icon: Shield,
      trend: keyStats.stats.obfuscations > 0 ? 'Ready' : 'Used up'
    },
    {
      title: 'Attacks Blocked',
      value: keyStats.stats.attacks_blocked,
      description: 'Security incidents',
      icon: Zap,
      trend: keyStats.stats.attacks_blocked === 0 ? 'Secure' : 'Protected'
    },
    {
      title: 'Banned Users',
      value: bannedUsers,
      description: 'Blacklisted accounts',
      icon: Users,
      trend: bannedUsers === 0 ? 'Clean' : 'Managed'
    }
  ]

  return (
    <>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {cards.map((card, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <card.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {card.description}
              </p>
              {card.trend && (
                <div className="mt-2">
                  <Badge variant="secondary" className="text-xs">
                    {card.trend}
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
      <CorsWarning isVisible={corsError} onClose={() => setCorsError(false)} />
    </>
  )
} 
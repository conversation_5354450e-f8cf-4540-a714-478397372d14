import { Server, Socket } from 'socket.io'
import { createServer } from 'http'
import { NextApiRequest } from 'next'
import { parse } from 'url'

interface ConnectionData {
  id: string
  userId: string
  scriptId: string
  hwid: string
  connectedAt: number
  lastPing: number
  gameInfo?: {
    gameId: string
    placeName: string
    placeId: string
  }
  executorInfo?: {
    name: string
    version: string
  }
}

interface ExecutionData {
  userId: string
  scriptId: string
  timestamp: number
  hwid: string
  discordId?: string
  gameId?: string
  placeName?: string
  executorName?: string
}

class WebSocketServer {
  private io: Server | null = null
  private connections: Map<string, ConnectionData> = new Map()
  private executionsToday: ExecutionData[] = []
  private peakConcurrent = 0

  constructor() {
    this.resetDailyStats()
  }

  initialize(httpServer: any) {
    if (this.io) return

    this.io = new Server(httpServer, {
      cors: {
        origin: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
        methods: ['GET', 'POST']
      }
    })

    this.io.on('connection', (socket: Socket) => {
      console.log('Client connected:', socket.id)

      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id)
      })

      socket.emit('stats:update', {
        totalToday: this.executionsToday.length,
        peakConcurrent: this.peakConcurrent
      })

      this.connections.forEach(conn => {
        socket.emit('connection:new', conn)
      })

      this.executionsToday.slice(-100).forEach(exec => {
        socket.emit('execution:new', exec)
      })
    })
  }

  addConnection(data: ConnectionData) {
    this.connections.set(data.id, data)
    this.peakConcurrent = Math.max(this.peakConcurrent, this.connections.size)
    
    if (this.io) {
      this.io.emit('connection:new', data)
      this.io.emit('stats:update', {
        totalToday: this.executionsToday.length,
        peakConcurrent: this.peakConcurrent
      })
    }
  }

  updateConnection(connectionId: string, data: Partial<ConnectionData>) {
    const conn = this.connections.get(connectionId)
    if (conn) {
      Object.assign(conn, data)
      if (this.io) {
        this.io.emit('connection:update', { id: connectionId, ...data })
      }
    }
  }

  removeConnection(connectionId: string) {
    this.connections.delete(connectionId)
    if (this.io) {
      this.io.emit('connection:remove', connectionId)
    }
  }

  addExecution(data: ExecutionData) {
    this.executionsToday.push(data)
    if (this.io) {
      this.io.emit('execution:new', data)
      this.io.emit('stats:update', {
        totalToday: this.executionsToday.length,
        peakConcurrent: this.peakConcurrent
      })
    }
  }

  private resetDailyStats() {
    const midnight = new Date()
    midnight.setHours(24, 0, 0, 0)
    const msUntilMidnight = midnight.getTime() - Date.now()

    setTimeout(() => {
      this.executionsToday = []
      this.peakConcurrent = this.connections.size
      this.resetDailyStats()
    }, msUntilMidnight)
  }
}

export const wsServer = new WebSocketServer()

export function initializeWebSocketServer(httpServer: any) {
  wsServer.initialize(httpServer)
  return wsServer
} 
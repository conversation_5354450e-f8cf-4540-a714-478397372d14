"use client"

import { useEffect, useState } from "react"
import { Bar, <PERSON>hart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YA<PERSON>s } from "recharts"

export function UserActivityChart() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className="flex items-center justify-center h-[300px] w-full bg-muted/20 rounded-md">
        <p className="text-muted-foreground">Loading chart...</p>
      </div>
    )
  }

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 10, right: 10, left: 10, bottom: 20 }}>
          <XAxis dataKey="hour" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
          <YAxis
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `${value}`}
          />
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <Tooltip
            formatter={(value) => [`${value}`, "Active Users"]}
            contentStyle={{
              backgroundColor: "hsl(var(--background))",
              borderColor: "hsl(var(--border))",
              borderRadius: "var(--radius)",
            }}
          />
          <Bar dataKey="activity" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

const data = [
  { hour: "00", activity: 12 },
  { hour: "02", activity: 8 },
  { hour: "04", activity: 5 },
  { hour: "06", activity: 15 },
  { hour: "08", activity: 45 },
  { hour: "10", activity: 78 },
  { hour: "12", activity: 92 },
  { hour: "14", activity: 134 },
  { hour: "16", activity: 156 },
  { hour: "18", activity: 189 },
  { hour: "20", activity: 167 },
  { hour: "22", activity: 89 },
]

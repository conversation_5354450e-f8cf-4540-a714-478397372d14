"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ArrowLeft, CalendarIcon, User, Shield, AlertTriangle, Save, CheckCircle, X } from "lucide-react"
import Link from "next/link"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface NotificationProps {
  message: string
  type: "success" | "error"
  isVisible: boolean
  onClose: () => void
}

function Notification({ message, type, isVisible, onClose }: NotificationProps) {
  if (!isVisible) return null

  return (
    <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-top-2 duration-300">
      <div
        className={cn(
          "flex items-center gap-3 px-4 py-3 rounded-lg shadow-lg border max-w-sm",
          type === "success"
            ? "bg-green-50 border-green-200 text-green-800 dark:bg-green-950/20 dark:border-green-800 dark:text-green-200"
            : "bg-red-50 border-red-200 text-red-800 dark:bg-red-950/20 dark:border-red-800 dark:text-red-200",
        )}
      >
        {type === "success" ? (
          <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
        ) : (
          <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
        )}
        <p className="text-sm font-medium flex-1">{message}</p>
        <Button variant="ghost" size="sm" className="h-auto p-1 hover:bg-transparent" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}

export default function UserProfilePage({ params }: { params: { id: string } }) {
  const user = users.find((u) => u.id === params.id) || users[0]

  const [expiryDate, setExpiryDate] = useState<Date | undefined>(new Date("2024-12-31"))
  const [notes, setNotes] = useState("Premium user with extended access privileges.")
  const [discordId, setDiscordId] = useState("user#1234")
  const [hwid, setHwid] = useState("ABC123XYZ789")
  const [isBanned, setIsBanned] = useState(false)
  const [banReason, setBanReason] = useState("")
  const [isCalendarOpen, setIsCalendarOpen] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  // Notification state
  const [notification, setNotification] = useState<{
    message: string
    type: "success" | "error"
    isVisible: boolean
  }>({
    message: "",
    type: "success",
    isVisible: false,
  })

  const showNotification = (message: string, type: "success" | "error" = "success") => {
    setNotification({
      message,
      type,
      isVisible: true,
    })

    // Auto-hide notification after 4 seconds
    setTimeout(() => {
      setNotification((prev) => ({ ...prev, isVisible: false }))
    }, 4000)
  }

  const hideNotification = () => {
    setNotification((prev) => ({ ...prev, isVisible: false }))
  }

  const handleSave = async () => {
    setIsSaving(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      console.log("Saving user profile:", {
        userId: params.id,
        expiryDate,
        notes,
        discordId,
        hwid,
        isBanned,
        banReason,
      })

      // Determine what was changed and show appropriate message
      const changesSummary = []
      if (notes.trim()) changesSummary.push("notes")
      if (discordId.trim()) changesSummary.push("Discord ID")
      if (hwid.trim()) changesSummary.push("HWID")
      if (expiryDate) changesSummary.push("expiry date")
      if (isBanned) changesSummary.push("ban status")

      const changesText =
        changesSummary.length > 0
          ? `User profile updated successfully! Changes saved: ${changesSummary.join(", ")}.`
          : "User profile updated successfully!"

      showNotification(changesText, "success")
    } catch (error) {
      console.error("Error saving user profile:", error)
      showNotification("Failed to save user profile. Please try again.", "error")
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="flex flex-col gap-6 max-w-4xl mx-auto">
      {/* Notification Component */}
      <Notification
        message={notification.message}
        type={notification.type}
        isVisible={notification.isVisible}
        onClose={hideNotification}
      />

      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/users">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight flex items-center gap-3">
            <User className="h-8 w-8" />
            {user.name} {user.surname}
          </h1>
          <p className="text-muted-foreground">User profile and account management</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={user.status === "Active" ? "default" : "secondary"}>{user.status}</Badge>
          {isBanned && (
            <Badge variant="destructive" className="gap-1">
              <Shield className="h-3 w-3" />
              Banned
            </Badge>
          )}
        </div>
      </div>

      <div className="grid gap-6">
        {/* Basic Information Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Basic Information
            </CardTitle>
            <CardDescription>Core user account details and identifiers</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="discordId" className="text-sm font-medium">
                  Discord ID
                </Label>
                <Input
                  id="discordId"
                  value={discordId}
                  onChange={(e) => setDiscordId(e.target.value)}
                  placeholder="Enter Discord ID"
                  className="font-mono"
                />
                <p className="text-xs text-muted-foreground">User's Discord identifier for communication</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="hwid" className="text-sm font-medium">
                  Hardware ID (HWID)
                </Label>
                <Input
                  id="hwid"
                  value={hwid}
                  onChange={(e) => setHwid(e.target.value)}
                  placeholder="Enter Hardware ID"
                  className="font-mono"
                />
                <p className="text-xs text-muted-foreground">Unique hardware identifier for device tracking</p>
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">Expiry Date</Label>
              <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn("w-full justify-start text-left font-normal", !expiryDate && "text-muted-foreground")}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {expiryDate ? format(expiryDate, "PPP") : "Select expiry date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={expiryDate}
                    onSelect={(date) => {
                      setExpiryDate(date)
                      setIsCalendarOpen(false)
                    }}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <p className="text-xs text-muted-foreground">
                Account access will expire on this date. Leave empty for permanent access.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Notes Section */}
        <Card>
          <CardHeader>
            <CardTitle>Notes & Comments</CardTitle>
            <CardDescription>Additional information and administrative notes</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="notes" className="text-sm font-medium">
                Administrative Notes
              </Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add any relevant notes about this user..."
                className="min-h-[120px] resize-none"
              />
              <p className="text-xs text-muted-foreground">
                Internal notes visible only to administrators. Use this space for important user information.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Security & Restrictions */}
        <Card
          className={cn("border-2", isBanned && "border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/20")}
        >
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security & Restrictions
            </CardTitle>
            <CardDescription>Account restrictions and security measures</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="space-y-1">
                <div className="font-medium flex items-center gap-2">
                  Ban/Blacklist User
                  {isBanned && <AlertTriangle className="h-4 w-4 text-red-500" />}
                </div>
                <div className="text-sm text-muted-foreground">
                  {isBanned
                    ? "This user is currently banned from accessing the platform"
                    : "Restrict user access to the platform"}
                </div>
              </div>
              <Switch checked={isBanned} onCheckedChange={setIsBanned} className="data-[state=checked]:bg-red-600" />
            </div>

            {isBanned && (
              <div className="space-y-4">
                <Alert className="border-red-200 bg-red-50 dark:bg-red-950/20">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800 dark:text-red-200">
                    This user is currently banned. They will not be able to access any platform services.
                  </AlertDescription>
                </Alert>

                <div className="space-y-2">
                  <Label htmlFor="banReason" className="text-sm font-medium text-red-700 dark:text-red-300">
                    Ban Reason *
                  </Label>
                  <Textarea
                    id="banReason"
                    value={banReason}
                    onChange={(e) => setBanReason(e.target.value)}
                    placeholder="Specify the reason for banning this user..."
                    className="min-h-[80px] border-red-200 focus:border-red-300 focus:ring-red-200"
                    required={isBanned}
                  />
                  <p className="text-xs text-red-600 dark:text-red-400">
                    A ban reason is required and will be logged for administrative records.
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <Button variant="outline" asChild>
            <Link href="/users">Cancel</Link>
          </Button>
          <Button onClick={handleSave} disabled={(isBanned && !banReason.trim()) || isSaving} className="gap-2">
            <Save className="h-4 w-4" />
            {isSaving ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </div>
    </div>
  )
}

const users = [
  {
    id: "1",
    name: "Thabo",
    surname: "Mbeki",
    email: "<EMAIL>",
    idType: "ID",
    phone: "+27 71 234 5678",
    status: "Active",
  },
  {
    id: "2",
    name: "Nomzamo",
    surname: "Mbatha",
    email: "<EMAIL>",
    idType: "Passport",
    phone: "+27 82 345 6789",
    status: "Active",
  },
  {
    id: "3",
    name: "Siya",
    surname: "Kolisi",
    email: "<EMAIL>",
    idType: "ID",
    phone: "+27 63 456 7890",
    status: "Active",
  },
  {
    id: "4",
    name: "Bonang",
    surname: "Matheba",
    email: "<EMAIL>",
    idType: "ID",
    phone: "+27 74 567 8901",
    status: "Pending",
  },
  {
    id: "5",
    name: "Trevor",
    surname: "Noah",
    email: "<EMAIL>",
    idType: "Passport",
    phone: "+27 85 678 9012",
    status: "Active",
  },
]

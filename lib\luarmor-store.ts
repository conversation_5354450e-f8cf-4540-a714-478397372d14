'use client'

import * as React from 'react'
import { LuarmorApiService, LuarmorKeyDetails, LuarmorKeyStats, LuarmorUser, createLuarmorApi } from './luarmor-api'

interface LuarmorStore {
  apiKey: string | null
  setApiKey: (key: string) => void
  api: LuarmorApiService | null
  keyDetails: LuarmorKeyDetails | null
  keyStats: LuarmorKeyStats | null
  users: LuarmorUser[]
  loading: boolean
  error: string | null
  corsError: boolean
  setCorsError: (value: boolean) => void
  selectedProject: string | null
  setSelectedProject: (projectId: string | null) => void
  refreshData: () => Promise<void>
  refreshUsers: () => Promise<void>
}

const LuarmorContext = React.createContext<LuarmorStore | null>(null)

export function LuarmorProvider({ children }: { children: React.ReactNode }) {
  const [api, setApi] = React.useState<LuarmorApiService | null>(null)
  const [keyDetails, setKeyDetails] = React.useState<LuarmorKeyDetails | null>(null)
  const [keyStats, setKeyStats] = React.useState<LuarmorKeyStats | null>(null)
  const [users, setUsers] = React.useState<LuarmorUser[]>([])
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [corsError, setCorsError] = React.useState(false)
  const [selectedProject, setSelectedProject] = React.useState<string | null>(null)

  const apiKey = process.env.NEXT_PUBLIC_LUARMOR_API_KEY || null

  const setApiKey = () => {
    console.warn('API key is now managed through environment variables')
  }

  React.useEffect(() => {
    if (apiKey) {
      const newApi = createLuarmorApi(apiKey)
      setApi(newApi)
    } else {
      setError('Luarmor API key not found. Please set NEXT_PUBLIC_LUARMOR_API_KEY in your .env.local file')
    }
  }, [])

  const refreshData = async () => {
    if (!api) return

    setLoading(true)
    setError(null)
    setCorsError(false)

    try {
      const [details, stats] = await Promise.all([
        api.getApiKeyDetails(),
        api.getApiKeyStats()
      ])

      setKeyDetails(details)
      setKeyStats(stats)

      if (details.projects.length > 0 && !selectedProject) {
        setSelectedProject(details.projects[0].id)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch data'
      if (errorMessage.includes('Failed to fetch') || errorMessage.includes('CORS') || errorMessage.includes('Access to fetch')) {
        setCorsError(true)
        setError('CORS policy is blocking the request. Please whitelist your domain with Luarmor.')
      } else {
        setError(errorMessage)
      }
    } finally {
      setLoading(false)
    }
  }

  const refreshUsers = async () => {
    if (!api || !selectedProject) return

    setLoading(true)
    setCorsError(false)
    try {
      const response = await api.getUsers(selectedProject)
      setUsers(response.users)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch users'
      if (errorMessage.includes('Failed to fetch') || errorMessage.includes('CORS') || errorMessage.includes('Access to fetch')) {
        setCorsError(true)
        setError('CORS policy is blocking the request. Please whitelist your domain with Luarmor.')
      } else {
        setError(errorMessage)
      }
    } finally {
      setLoading(false)
    }
  }

  React.useEffect(() => {
    if (api) {
      refreshData()
    }
  }, [api])

  React.useEffect(() => {
    if (selectedProject) {
      refreshUsers()
    }
  }, [selectedProject])

  const value: LuarmorStore = {
    apiKey,
    setApiKey,
    api,
    keyDetails,
    keyStats,
    users,
    loading,
    error,
    corsError,
    setCorsError,
    selectedProject,
    setSelectedProject,
    refreshData,
    refreshUsers
  }

  return React.createElement(
    LuarmorContext.Provider,
    { value },
    children
  )
}

export function useLuarmor() {
  const context = React.useContext(LuarmorContext)
  if (!context) {
    throw new Error('useLuarmor must be used within a LuarmorProvider')
  }
  return context
} 
export interface LuarmorApiStatus {
  version: string
  active: boolean
  message: string
  warning: boolean
  warning_message: string
}

export interface LuarmorScript {
  script_name: string
  script_id: string
  script_version: string
  ffa: boolean
  silent: boolean
}

export interface LuarmorProject {
  platform: string
  id: string
  name: string
  settings: {
    reset_hwid_cooldown: number
  }
  scripts: LuarmorScript[]
}

export interface LuarmorKeyDetails {
  success: boolean
  message: string
  email: string
  discord_id: string
  expires_at: number
  registered_at: number
  plan: string
  enabled: number
  projects: LuarmorProject[]
}

export interface LuarmorKeyStats {
  success: boolean
  message: string
  execution_data: {
    frequency: number
    executions: number[]
  }
  stats: {
    obfuscations: number
    scripts: number
    users: number
    attacks_blocked: number
    default: {
      scripts: number
      users: number
      obfuscations: number
    }
    reset_at: number
  }
}

export interface LuarmorUser {
  user_key: string
  identifier: string
  identifier_type: string
  discord_id: string
  status: 'active' | 'reset' | 'banned'
  last_reset: number
  total_resets: number
  auth_expire: number
  banned: number
  ban_reason: string
  ban_expire: number
  unban_token: string
  total_executions: number
  note: string
  ban_ip: string
}

export interface LuarmorUsersResponse {
  success: boolean
  message: string
  users: LuarmorUser[]
}

export interface CreateUserRequest {
  identifier?: string
  auth_expire?: number
  note?: string
  discord_id?: string
  key_days?: number
}

export interface CreateUserResponse {
  success: boolean
  message: string
  user_key: string
}

export interface UpdateUserRequest {
  user_key: string
  identifier?: string
  auth_expire?: number
  note?: string
  discord_id?: string
}

const BASE_URL = 'https://api.luarmor.net'

class LuarmorApiService {
  private apiKey: string

  constructor(apiKey: string) {
    this.apiKey = apiKey
  }

  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${BASE_URL}${endpoint}`
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': this.apiKey,
        ...options.headers,
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  async getApiStatus(): Promise<LuarmorApiStatus> {
    return this.makeRequest<LuarmorApiStatus>('/status')
  }

  async getApiKeyDetails(): Promise<LuarmorKeyDetails> {
    return this.makeRequest<LuarmorKeyDetails>(`/v3/keys/${this.apiKey}/details`)
  }

  async getApiKeyStats(noUsers?: boolean): Promise<LuarmorKeyStats> {
    const params = noUsers ? '?noUsers=true' : ''
    return this.makeRequest<LuarmorKeyStats>(`/v3/keys/${this.apiKey}/stats${params}`)
  }

  async getUsers(projectId: string, filters?: {
    discord_id?: string
    user_key?: string
    identifier?: string
    from?: number
    until?: number
    search?: string
  }): Promise<LuarmorUsersResponse> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString())
        }
      })
    }
    
    const queryString = params.toString() ? `?${params.toString()}` : ''
    return this.makeRequest<LuarmorUsersResponse>(`/v3/projects/${projectId}/users${queryString}`)
  }

  async createUser(projectId: string, userData: CreateUserRequest): Promise<CreateUserResponse> {
    return this.makeRequest<CreateUserResponse>(`/v3/projects/${projectId}/users`, {
      method: 'POST',
      body: JSON.stringify(userData),
    })
  }

  async updateUser(projectId: string, userData: UpdateUserRequest): Promise<{ success: boolean; message: string }> {
    return this.makeRequest<{ success: boolean; message: string }>(`/v3/projects/${projectId}/users`, {
      method: 'PATCH',
      body: JSON.stringify(userData),
    })
  }

  async deleteUser(projectId: string, userKey: string): Promise<{ success: boolean; message: string }> {
    return this.makeRequest<{ success: boolean; message: string }>(`/v3/projects/${projectId}/users?user_key=${userKey}`, {
      method: 'DELETE',
    })
  }

  async resetUserHwid(projectId: string, userKey: string, force?: boolean): Promise<{ success: boolean; message: string }> {
    return this.makeRequest<{ success: boolean; message: string }>(`/v3/projects/${projectId}/users/resethwid`, {
      method: 'POST',
      body: JSON.stringify({ user_key: userKey, force }),
    })
  }

  async linkDiscordId(projectId: string, userKey: string, discordId: string, force?: boolean): Promise<{ success: boolean; message: string }> {
    return this.makeRequest<{ success: boolean; message: string }>(`/v3/projects/${projectId}/users/linkdiscord`, {
      method: 'POST',
      body: JSON.stringify({ user_key: userKey, discord_id: discordId, force }),
    })
  }

  async blacklistUser(projectId: string, userKey: string, banReason: string, banExpire?: number): Promise<{ success: boolean; message: string }> {
    return this.makeRequest<{ success: boolean; message: string }>(`/v3/projects/${projectId}/users/blacklist`, {
      method: 'POST',
      body: JSON.stringify({ user_key: userKey, ban_reason: banReason, ban_expire: banExpire }),
    })
  }

  async updateScript(projectId: string, scriptId: string, scriptData: {
    script: string
    silent?: boolean
    ffa?: boolean
    heartbeat?: boolean
    lightning?: boolean
  }): Promise<{ success: boolean; message: string }> {
    return this.makeRequest<{ success: boolean; message: string }>(`/v3/projects/${projectId}/scripts/${scriptId}`, {
      method: 'PUT',
      body: JSON.stringify(scriptData),
    })
  }
}

export { LuarmorApiService }

export const createLuarmorApi = (apiKey: string) => new LuarmorApiService(apiKey) 
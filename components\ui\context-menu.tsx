"use client"

import React, { useState, useEffect, useRef } from 'react'
import { Co<PERSON>, Clipboard, Check } from 'lucide-react'

interface ContextMenuProps {
  x: number
  y: number
  onClose: () => void
  selectedText?: string
}

export const ContextMenu: React.FC<ContextMenuProps> = ({ x, y, onClose, selectedText }) => {
  const [copied, setCopied] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    document.addEventListener('keydown', handleEscape)
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [onClose])

  useEffect(() => {
    if (menuRef.current) {
      const rect = menuRef.current.getBoundingClientRect()
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      let adjustedX = x
      let adjustedY = y

      if (x + rect.width > viewportWidth) {
        adjustedX = viewportWidth - rect.width - 10
      }

      if (y + rect.height > viewportHeight) {
        adjustedY = viewportHeight - rect.height - 10
      }

      menuRef.current.style.left = `${adjustedX}px`
      menuRef.current.style.top = `${adjustedY}px`
    }
  }, [x, y])

  const handleCopy = async () => {
    try {
      const textToCopy = selectedText || window.getSelection()?.toString() || ''
      if (textToCopy) {
        await navigator.clipboard.writeText(textToCopy)
        setCopied(true)
        setTimeout(() => {
          setCopied(false)
          onClose()
        }, 1000)
      }
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText()
      const activeElement = document.activeElement as HTMLInputElement | HTMLTextAreaElement
      
      if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
        const start = activeElement.selectionStart || 0
        const end = activeElement.selectionEnd || 0
        const value = activeElement.value
        
        activeElement.value = value.slice(0, start) + text + value.slice(end)
        activeElement.setSelectionRange(start + text.length, start + text.length)
        activeElement.focus()
      }
      onClose()
    } catch (err) {
      console.error('Failed to paste text: ', err)
    }
  }

  return (
    <div
      ref={menuRef}
      className="fixed z-[9999] bg-slate-800/80 backdrop-blur-lg border border-slate-700/40 rounded-xl shadow-2xl shadow-black/30 p-2 min-w-[160px] animate-in fade-in-0 zoom-in-95 duration-200"
      style={{ left: x, top: y }}
    >
      <div className="space-y-1">
        <button
          onClick={handleCopy}
          className="w-full flex items-center gap-3 px-3 py-2 text-sm rounded-lg hover:bg-slate-700/50 transition-all duration-200 group"
          disabled={!selectedText && !window.getSelection()?.toString()}
        >
          {copied ? (
            <Check className="w-4 h-4 text-emerald-400" />
          ) : (
            <Copy className="w-4 h-4 text-slate-400 group-hover:text-blue-400 transition-colors" />
          )}
          <span className="font-medium text-slate-200">
            {copied ? 'Copied!' : 'Copy'}
          </span>
        </button>
        
        <button
          onClick={handlePaste}
          className="w-full flex items-center gap-3 px-3 py-2 text-sm rounded-lg hover:bg-slate-700/50 transition-all duration-200 group"
        >
          <Clipboard className="w-4 h-4 text-slate-400 group-hover:text-blue-400 transition-colors" />
          <span className="font-medium text-slate-200">Paste</span>
        </button>
      </div>
    </div>
  )
}

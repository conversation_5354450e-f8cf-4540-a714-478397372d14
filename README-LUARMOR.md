# Luarmor API Integration Setup

This admin dashboard is integrated with the Luarmor API for managing users, scripts, and analytics.

## Configuration

### 1. Environment Setup

Create a `.env.local` file in your project root directory:

```bash
# .env.local
NEXT_PUBLIC_LUARMOR_API_KEY=your_actual_api_key_here
```

### 2. Get Your API Key

1. Log in to your Luarmor dashboard
2. Navigate to API settings
3. Copy your API key
4. Replace `your_actual_api_key_here` with your actual API key

### 3. Important Security Notes

- **Never commit your `.env.local` file to version control**
- The `.env.local` file is already added to `.gitignore`
- Do not share your API key publicly
- Rotate your API key regularly for security

### 4. Required API Settings

Make sure your Luarmor API key has the following permissions:
- Read/Write access to users
- Read access to statistics
- Read/Write access to scripts

### 5. IP Whitelisting

**Important**: You must whitelist your server's IP address in the Luarmor dashboard:
1. Go to Luarmor dashboard
2. Navigate to API settings
3. Add your server's IP address to the whitelist
4. For development, add your local IP address

### 6. Start the Application

After setting up your API key:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

## Features

Once configured, the dashboard provides:

- **User Management**: Create, ban, reset HWID, link Discord
- **Analytics**: Execution charts, user statistics, resource usage
- **Project Management**: Switch between multiple projects
- **Real-time Data**: Live updates from the Luarmor API

## Troubleshooting

If you see "Luarmor API Not Configured":
1. Ensure `.env.local` file exists in the project root
2. Check that the API key is correctly formatted
3. Restart your development server after adding the API key
4. Verify your IP is whitelisted in Luarmor dashboard

## API Rate Limits

The Luarmor API has the following rate limits:
- 60 requests per minute per endpoint
- Exceeding limits will result in a 429 error

## Support

For API-related issues, contact Luarmor support.
For dashboard issues, check the project repository. 
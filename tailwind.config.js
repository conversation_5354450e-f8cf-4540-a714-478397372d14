/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        "roll-left": {
          "0%": {
            transform: "rotateY(0deg) translateZ(0px)",
            opacity: "1"
          },
          "50%": {
            transform: "rotateY(-90deg) translateZ(-200px)",
            opacity: "0.3"
          },
          "100%": {
            transform: "rotateY(-180deg) translateZ(0px)",
            opacity: "1"
          },
        },
        "roll-right": {
          "0%": {
            transform: "rotateY(0deg) translateZ(0px)",
            opacity: "1"
          },
          "50%": {
            transform: "rotateY(90deg) translateZ(-200px)",
            opacity: "0.3"
          },
          "100%": {
            transform: "rotateY(180deg) translateZ(0px)",
            opacity: "1"
          },
        },
        "slide-in-left": {
          "0%": {
            transform: "translateX(-100%) rotateY(-15deg)",
            opacity: "0"
          },
          "100%": {
            transform: "translateX(0%) rotateY(0deg)",
            opacity: "1"
          },
        },
        "slide-in-right": {
          "0%": {
            transform: "translateX(100%) rotateY(15deg)",
            opacity: "0"
          },
          "100%": {
            transform: "translateX(0%) rotateY(0deg)",
            opacity: "1"
          },
        },
        "slide-out-left": {
          "0%": {
            transform: "translateX(0%) rotateY(0deg)",
            opacity: "1"
          },
          "100%": {
            transform: "translateX(-100%) rotateY(-15deg)",
            opacity: "0"
          },
        },
        "slide-out-right": {
          "0%": {
            transform: "translateX(0%) rotateY(0deg)",
            opacity: "1"
          },
          "100%": {
            transform: "translateX(100%) rotateY(15deg)",
            opacity: "0"
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "roll-left": "roll-left 0.8s cubic-bezier(0.4, 0, 0.2, 1)",
        "roll-right": "roll-right 0.8s cubic-bezier(0.4, 0, 0.2, 1)",
        "slide-in-left": "slide-in-left 0.6s cubic-bezier(0.4, 0, 0.2, 1)",
        "slide-in-right": "slide-in-right 0.6s cubic-bezier(0.4, 0, 0.2, 1)",
        "slide-out-left": "slide-out-left 0.6s cubic-bezier(0.4, 0, 0.2, 1)",
        "slide-out-right": "slide-out-right 0.6s cubic-bezier(0.4, 0, 0.2, 1)",
      },
      perspective: {
        '500': '500px',
        '1000': '1000px',
        '1500': '1500px',
        '2000': '2000px',
      },
      transformStyle: {
        'preserve-3d': 'preserve-3d',
        'flat': 'flat',
      },
      backfaceVisibility: {
        'hidden': 'hidden',
        'visible': 'visible',
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    function({ addUtilities }) {
      const newUtilities = {
        '.transform-style-preserve-3d': {
          'transform-style': 'preserve-3d',
        },
        '.transform-style-flat': {
          'transform-style': 'flat',
        },
        '.backface-hidden': {
          'backface-visibility': 'hidden',
        },
        '.backface-visible': {
          'backface-visibility': 'visible',
        },
        '.perspective-500': {
          'perspective': '500px',
        },
        '.perspective-1000': {
          'perspective': '1000px',
        },
        '.perspective-1500': {
          'perspective': '1500px',
        },
        '.perspective-2000': {
          'perspective': '2000px',
        },
      }
      addUtilities(newUtilities)
    }
  ],
}

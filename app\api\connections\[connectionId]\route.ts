import { NextRequest, NextResponse } from 'next/server'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
}

export async function OPTIONS(request: NextRequest) {
  return new Response(null, { status: 200, headers: corsHeaders })
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ connectionId: string }> }
) {
  try {
    const requestStartTime = Date.now()
    const { connectionId } = await params
    const body = await request.json()

    console.log('[<PERSON>arm<PERSON>] Heartbeat received for:', connectionId)

    // Calculate ping if client timestamp is provided
    let pingMs = null
    if (body.clientTimestamp) {
      pingMs = requestStartTime - body.clientTimestamp
      if (pingMs < 0) pingMs = 0 // Handle clock sync issues
    }

    // Get the active connections from the main route
    const { activeConnections } = await import('../route')
    const connection = activeConnections.get(connectionId)
    
    if (connection && pingMs !== null) {
      // Update ping statistics
      const pingData = connection.ping || { current: 0, average: 0, min: 0, max: 0, history: [] }
      
      // Update current ping
      pingData.current = Math.round(pingMs)
      
      // Update history (keep last 20 pings)
      pingData.history.push(pingData.current)
      if (pingData.history.length > 20) {
        pingData.history.shift()
      }
      
      // Calculate statistics
      if (pingData.history.length > 0) {
        pingData.average = Math.round(pingData.history.reduce((a: number, b: number) => a + b, 0) / pingData.history.length)
        pingData.min = Math.min(...pingData.history)
        pingData.max = Math.max(...pingData.history)
      }
      
      // Update connection data
      connection.ping = pingData
      connection.lastPing = Date.now()
      activeConnections.set(connectionId, connection)
    }

    // Update connection data
    const updateData = {
      connectionId,
      lastPing: Date.now(),
      ping: pingMs,
      ...body
    }

    // Check for pending admin actions
    const response: any = {
      success: true,
      message: 'Connection updated',
      data: updateData,
      timestamp: Date.now(),
      serverTimestamp: requestStartTime,
      pingMs: pingMs
    }

    try {
      const { getPendingAction } = await import('../../admin/actions/route')
      const pendingAction = getPendingAction(connectionId)
      
      if (pendingAction) {
        console.log(`[Luarmor] Executing pending ${pendingAction.action} for connection ${connectionId}`)
        
        switch (pendingAction.action) {
          case 'kick':
            response.adminAction = 'kick'
            response.reason = pendingAction.reason
            response.message = `You have been kicked from the server. Reason: ${pendingAction.reason || 'No reason provided'}`
            console.log(`[Luarmor] Sending kick command to ${connectionId}`)
            
            // Remove connection immediately after kick
            setTimeout(() => {
              activeConnections.delete(connectionId)
              console.log(`[Luarmor] 🔧 Connection ${connectionId} removed after kick`)
            }, 2000) // Give 2 seconds for client to receive the kick command
            break
            
          case 'ban':
            response.adminAction = 'ban'
            response.reason = pendingAction.reason
            response.message = `You have been banned from the server. Reason: ${pendingAction.reason || 'No reason provided'}`
            console.log(`[Luarmor] Sending ban command to ${connectionId}`)
            
            // Remove connection immediately after ban
            setTimeout(() => {
              activeConnections.delete(connectionId)
              console.log(`[Luarmor] 🔧 Connection ${connectionId} removed after ban`)
            }, 2000) // Give 2 seconds for client to receive the ban command
            break
            
          case 'announcement':
            response.adminAction = 'announcement'
            response.message = pendingAction.message
            console.log(`[Luarmor] Sending announcement to ${connectionId}: ${pendingAction.message}`)
            // Don't remove connection for announcements
            break
        }
      }
    } catch (actionError) {
      console.error('[Luarmor] Error checking pending actions:', actionError)
    }

    // Log ping information if available
    if (pingMs !== null) {
      console.log(`[Luarmor] Connection ${connectionId} ping: ${pingMs}ms`)
    }

    return NextResponse.json(response, { headers: corsHeaders })
  } catch (error) {
    console.error('[Luarmor] Update error:', error)
    return NextResponse.json(
      { error: 'Failed to update connection' },
      { status: 500, headers: corsHeaders }
    )
  }
} 
--[[
    Luarmor Force Kick Script
    This script uses multiple aggressive methods to ensure the player gets kicked
    Use this version if the normal kick isn't working
]]

-- Prevent multiple executions
if _G.LuarmorForceKick then
    warn("[Force Kick] Already running, stopping...")
    return
end
_G.LuarmorForceKick = true

local HttpService = game:GetService("HttpService")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- Configuration
local API_URL = "http://localhost:3002/api/connections"
local SCRIPT_ID = "luarmor_force_kick_v1"
local HEARTBEAT_INTERVAL = 3 -- Send heartbeat every 3 seconds

-- Get player info
local player = Players.LocalPlayer
local userId = tostring(player.UserId)
local playerName = player.Name or "Unknown"

print("[Force Kick] 🚀 Starting aggressive kick client...")
print("[Force Kick] 👤 User:", playerName, "(" .. userId .. ")")

-- Connection state
local connectionId = nil
local isConnected = false
local kickReceived = false
local heartbeatCount = 0
local currentPing = 0
local avgPing = 0
local pingHistory = {}

-- Function to send HTTP request safely
local function sendRequest(method, endpoint, data)
    local startTime = tick() -- Start timing for ping calculation
    
    local success, response = pcall(function()
        return request({
            Url = API_URL .. (endpoint or ""),
            Method = method,
            Headers = {
                ["Content-Type"] = "application/json"
            },
            Body = data and HttpService:JSONEncode(data) or nil
        })
    end)
    
    local endTime = tick()
    local requestTime = (endTime - startTime) * 1000 -- Convert to milliseconds
    
    if success and response then
        if response.StatusCode and (response.StatusCode == 200 or response.StatusCode == 201) then
            local bodySuccess, bodyData = pcall(function()
                return response.Body and HttpService:JSONDecode(response.Body) or {}
            end)
            
            -- Add ping info to response
            if bodySuccess and bodyData then
                bodyData._pingMs = requestTime
            end
            
            return bodySuccess, bodyData
        end
    end
    return false, response
end

-- Function to update ping statistics
local function updatePingStats(pingMs)
    currentPing = math.floor(pingMs)
    
    -- Add to ping history (keep last 10 pings)
    table.insert(pingHistory, currentPing)
    if #pingHistory > 10 then
        table.remove(pingHistory, 1)
    end
    
    -- Calculate average ping
    local total = 0
    for _, ping in ipairs(pingHistory) do
        total = total + ping
    end
    avgPing = math.floor(total / #pingHistory)
end

-- Aggressive kick function
local function forceKick(reason)
    if kickReceived then return end
    kickReceived = true
    
    warn("[Force Kick] 🔥 EXECUTING AGGRESSIVE KICK")
    warn("[Force Kick] 🔥 Reason:", reason or "Admin action")
    
    -- Method 1: Direct kick
    pcall(function()
        game.Players.LocalPlayer:Kick("🚫 [PULSE HUB ADMIN] " .. (reason or "You have been kicked"))
    end)
    
    -- Method 2: Player variable kick
    pcall(function()
        player:Kick("🚫 [PULSE HUB ADMIN] " .. (reason or "You have been kicked"))
    end)
    
    -- Method 3: Shutdown game
    spawn(function()
        wait(1)
        pcall(function()
            game:Shutdown()
        end)
    end)
    
    -- Method 4: Teleport to invalid place (causes disconnect)
    spawn(function()
        wait(2)
        pcall(function()
            game:GetService("TeleportService"):Teleport(0, player)
        end)
    end)
    
    -- Method 5: Crash the client (extreme method)
    spawn(function()
        wait(3)
        warn("[Force Kick] 🔥 Attempting client crash...")
        local function crashLoop()
            while true do
                crashLoop()
            end
        end
        pcall(crashLoop)
    end)
    
    -- Method 6: Memory overload
    spawn(function()
        wait(4)
        warn("[Force Kick] 🔥 Attempting memory overload...")
        local bigTable = {}
        for i = 1, 999999 do
            bigTable[i] = string.rep("crash", 1000)
        end
    end)
end

-- Establish connection
local function establishConnection()
    print("[Force Kick] 🔄 Connecting...")
    local success, response = sendRequest("POST", "", {
        userId = userId,
        scriptId = SCRIPT_ID,
        hwid = HttpService:GenerateGUID(false),
        playerName = playerName,
        gameInfo = {
            gameId = tostring(game.GameId or 0),
            placeId = tostring(game.PlaceId or 0),
            placeName = "Force Kick Test"
        },
        executorInfo = {
            name = "ForceKick",
            version = "1.0"
        }
    })
    
    if success and response and response.connectionId then
        connectionId = response.connectionId
        isConnected = true
        print("[Force Kick] ✅ Connected! ID:", connectionId)
        return true
    elseif response and response.error == "USER_BANNED" then
        warn("[Force Kick] ⚠️ USER IS BANNED")
        warn("[Force Kick] " .. (response.message or "You are banned"))
        forceKick("You are banned from this service")
        return false
    else
        warn("[Force Kick] ❌ Connection failed:", response)
        return false
    end
end

-- Send heartbeat
local function sendHeartbeat()
    if not connectionId or not isConnected or kickReceived then 
        return false
    end
    
    heartbeatCount = heartbeatCount + 1
    local heartbeatStartTime = tick()
    
    local success, response = sendRequest("PATCH", "/" .. connectionId, {
        lastPing = os.time(),
        status = "active",
        heartbeat = heartbeatCount,
        clientTimestamp = heartbeatStartTime * 1000 -- Send timestamp in milliseconds
    })
    
    if success and response then
        -- Calculate and update ping
        if response._pingMs then
            updatePingStats(response._pingMs)
        end
        
        print("[Force Kick] 💗 Heartbeat", heartbeatCount, "| Ping:", currentPing .. "ms", "| Avg:", avgPing .. "ms")
        
        -- Check for admin actions
        if response.adminAction then
            warn("[Force Kick] 🚨 Admin action received:", response.adminAction)
            if response.adminAction == "kick" then
                forceKick("Kicked: " .. (response.reason or "No reason"))
            elseif response.adminAction == "ban" then
                forceKick("Banned: " .. (response.reason or "No reason"))
            elseif response.adminAction == "announcement" then
                warn("[Force Kick] 📢 ADMIN: " .. (response.message or "No message"))
            end
        end
        return true
    else
        warn("[Force Kick] ❌ Heartbeat", heartbeatCount, "failed:", response)
        return false
    end
end

-- Reconnection function
local function attemptReconnection()
    warn("[Force Kick] 🔄 Attempting reconnection...")
    isConnected = false
    connectionId = nil
    heartbeatCount = 0
    
    wait(2) -- Wait 2 seconds before reconnecting
    
    if establishConnection() then
        print("[Force Kick] ✅ Reconnected successfully!")
        return true
    else
        warn("[Force Kick] ❌ Reconnection failed")
        return false
    end
end

-- Start connection
if establishConnection() then
    -- Enhanced heartbeat system with reconnection
    spawn(function()
        local failedHeartbeats = 0
        
        while _G.LuarmorForceKick and not kickReceived do
            wait(HEARTBEAT_INTERVAL)
            
            if isConnected and not kickReceived then
                local success = sendHeartbeat()
                
                if success then
                    failedHeartbeats = 0
                else
                    failedHeartbeats = failedHeartbeats + 1
                    warn("[Force Kick] ⚠️ Failed heartbeats:", failedHeartbeats)
                    
                    -- Try to reconnect after 3 failed heartbeats
                    if failedHeartbeats >= 3 then
                        warn("[Force Kick] 🔄 Too many failed heartbeats, reconnecting...")
                        if attemptReconnection() then
                            failedHeartbeats = 0
                        else
                            -- If reconnection fails, wait longer and try again
                            wait(5)
                        end
                    end
                end
            end
        end
    end)
    
    -- Cleanup on player leaving
    spawn(function()
        Players.PlayerRemoving:Connect(function(leavingPlayer)
            if leavingPlayer == player then
                _G.LuarmorForceKick = false
                warn("[Force Kick] 🔌 Player leaving, cleaning up...")
            end
        end)
    end)
    
    print("[Force Kick] ✅ Force kick client loaded!")
    print("[Force Kick] ⚡ Heartbeat every", HEARTBEAT_INTERVAL, "seconds")
    print("[Force Kick] ⚡ Will aggressively kick on admin command")
else
    warn("[Force Kick] ❌ Failed to connect")
end 
'use client'

import * as React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Ban, Calendar, Clock, Trash2, RefreshCw } from 'lucide-react'

interface BanData {
  userId: string
  reason: string
  bannedAt: number
  expiresAt: number | null
  bannedBy: string
  duration: string
  connectionId: string
}

export function BanManagement() {
  const [bans, setBans] = React.useState<BanData[]>([])
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)

  const fetchBans = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/admin/actions')
      const data = await response.json()
      
      if (data.success) {
        setBans(data.bans || [])
      } else {
        setError(data.error || 'Failed to fetch bans')
      }
    } catch (err) {
      setError('Error fetching ban data')
      console.error('Ban fetch error:', err)
    } finally {
      setLoading(false)
    }
  }

  React.useEffect(() => {
    fetchBans()
  }, [])

  const formatTimeRemaining = (expiresAt: number | null) => {
    if (!expiresAt) return 'Permanent'
    
    const remaining = expiresAt - Date.now()
    if (remaining <= 0) return 'Expired'
    
    const days = Math.floor(remaining / (24 * 60 * 60 * 1000))
    const hours = Math.floor((remaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
    const minutes = Math.floor((remaining % (60 * 60 * 1000)) / (60 * 1000))
    
    if (days > 0) {
      return `${days}d ${hours}h`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m`
    }
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString()
  }

  const removeBan = async (userId: string) => {
    // TODO: Implement ban removal API
    console.log('Remove ban for user:', userId)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <Ban className="h-5 w-5 text-red-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold">Ban Management</h2>
            <p className="text-muted-foreground">View and manage active user bans</p>
          </div>
        </div>
        <Button onClick={fetchBans} disabled={loading} variant="outline">
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bans</CardTitle>
            <Ban className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bans.length}</div>
            <p className="text-xs text-muted-foreground">Active bans</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Permanent Bans</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {bans.filter(ban => !ban.expiresAt).length}
            </div>
            <p className="text-xs text-muted-foreground">Never expire</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Temporary Bans</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {bans.filter(ban => ban.expiresAt).length}
            </div>
            <p className="text-xs text-muted-foreground">Will expire</p>
          </CardContent>
        </Card>
      </div>

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50 dark:bg-red-900/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-600">
              <Ban className="h-4 w-4" />
              <span className="font-medium">Error: {error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bans Table */}
      <Card>
        <CardHeader>
          <CardTitle>Active Bans</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
              <span>Loading bans...</span>
            </div>
          ) : bans.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Ban className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No active bans found</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User ID</TableHead>
                  <TableHead>Reason</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Banned At</TableHead>
                  <TableHead>Time Remaining</TableHead>
                  <TableHead>Banned By</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {bans.map((ban) => (
                  <TableRow key={ban.userId}>
                    <TableCell className="font-medium">{ban.userId}</TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate" title={ban.reason}>
                        {ban.reason}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={ban.expiresAt ? 'secondary' : 'destructive'}
                        className="text-xs"
                      >
                        {ban.expiresAt ? ban.duration : 'Permanent'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {formatDate(ban.bannedAt)}
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={ban.expiresAt ? 'outline' : 'destructive'}
                        className="text-xs"
                      >
                        {formatTimeRemaining(ban.expiresAt)}
                      </Badge>
                    </TableCell>
                    <TableCell>{ban.bannedBy}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeBan(ban.userId)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 
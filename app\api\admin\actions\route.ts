import { NextRequest, NextResponse } from 'next/server'
import { writeFile, readFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
}

export async function OPTIONS() {
  return new Response(null, { status: 200, headers: corsHeaders })
}

interface BanData {
  userId: string
  reason: string
  bannedAt: number
  expiresAt: number | null // null for permanent
  bannedBy: string
  duration: string
  connectionId: string
}

interface PendingAction {
  connectionId: string
  action: 'kick' | 'ban' | 'announcement'
  reason?: string
  message?: string
  timestamp: number
  executed?: boolean
}

interface ActionRequest {
  action: 'kick' | 'ban' | 'announcement'
  connectionId: string
  userId: string
  data: any
}

const DATA_DIR = path.join(process.cwd(), 'data')
const BANS_FILE = path.join(DATA_DIR, 'bans.json')
const PENDING_ACTIONS_FILE = path.join(DATA_DIR, 'pending-actions.json')

// In-memory storage for faster access
const pendingActions = new Map<string, PendingAction>()

// Ensure data directory exists
async function ensureDataDir() {
  if (!existsSync(DATA_DIR)) {
    await mkdir(DATA_DIR, { recursive: true })
  }
}

// Read bans from file
async function readBans(): Promise<BanData[]> {
  try {
    await ensureDataDir()
    if (!existsSync(BANS_FILE)) {
      return []
    }
    const data = await readFile(BANS_FILE, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading bans:', error)
    return []
  }
}

// Write bans to file
async function writeBans(bans: BanData[]) {
  try {
    await ensureDataDir()
    await writeFile(BANS_FILE, JSON.stringify(bans, null, 2))
  } catch (error) {
    console.error('Error writing bans:', error)
    throw error
  }
}

// Check if user is banned
export async function checkUserBan(userId: string): Promise<BanData | null> {
  const bans = await readBans()
  const activeBan = bans.find(ban => 
    ban.userId === userId && 
    (ban.expiresAt === null || ban.expiresAt > Date.now())
  )
  return activeBan || null
}

// Add pending action for a connection
export function addPendingAction(connectionId: string, action: PendingAction) {
  pendingActions.set(connectionId, action)
  console.log(`[Admin] Added pending action ${action.action} for connection ${connectionId}`)
}

// Get and remove pending action for a connection
export function getPendingAction(connectionId: string): PendingAction | null {
  const action = pendingActions.get(connectionId)
  if (action) {
    // For announcements, remove immediately after sending
    if (action.action === 'announcement') {
      pendingActions.delete(connectionId)
      console.log(`[Admin] Announcement sent and cleared for ${connectionId}`)
      return action
    }
    
    // For kick/ban, mark as executed and keep for logging
    if (action.action === 'kick' || action.action === 'ban') {
      action.executed = true
      // Keep it longer for disconnect tracking
      setTimeout(() => {
        pendingActions.delete(connectionId)
        console.log(`[Admin] Removed executed ${action.action} action for ${connectionId}`)
      }, 60000) // Remove after 60 seconds
      return action
    }
  }
  return null
}

// Check if disconnection was caused by admin action
export function wasAdminDisconnect(connectionId: string): PendingAction | null {
  const action = pendingActions.get(connectionId)
  return action && action.executed ? action : null
}

export async function POST(request: NextRequest) {
  try {
    const body: ActionRequest = await request.json()
    console.log('[Admin] Action request:', body)

    if (!body.action || !body.connectionId || !body.userId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400, headers: corsHeaders }
      )
    }

    const response: any = {
      success: true,
      action: body.action,
      userId: body.userId,
      connectionId: body.connectionId,
      timestamp: Date.now()
    }

    switch (body.action) {
      case 'kick':
        console.log(`[Admin] Kicking user ${body.userId}: ${body.data.reason}`)
        
        // Add pending kick action
        addPendingAction(body.connectionId, {
          connectionId: body.connectionId,
          action: 'kick',
          reason: body.data.reason,
          timestamp: Date.now()
        })
        
        response.message = 'User kick command sent'
        response.reason = body.data.reason
        break

      case 'ban':
        console.log(`[Admin] Banning user ${body.userId}: ${body.data.reason}`)
        
        // Add to bans
        const bans = await readBans()
        const newBan: BanData = {
          userId: body.userId,
          reason: body.data.reason,
          bannedAt: Date.now(),
          expiresAt: body.data.expiresAt,
          bannedBy: 'Admin',
          duration: body.data.duration,
          connectionId: body.connectionId
        }
        
        // Remove any existing bans for this user
        const filteredBans = bans.filter(ban => ban.userId !== body.userId)
        filteredBans.push(newBan)
        
        await writeBans(filteredBans)
        
        // Add pending ban action
        addPendingAction(body.connectionId, {
          connectionId: body.connectionId,
          action: 'ban',
          reason: body.data.reason,
          timestamp: Date.now()
        })
        
        response.message = 'User ban applied and kick command sent'
        response.banData = newBan
        break

      case 'announcement':
        console.log(`[Admin] Sending announcement to user ${body.userId}: ${body.data.message}`)
        
        // Add pending announcement action
        addPendingAction(body.connectionId, {
          connectionId: body.connectionId,
          action: 'announcement',
          message: body.data.message,
          timestamp: Date.now()
        })
        
        response.message = 'Announcement sent to user'
        response.announcement = body.data.message
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400, headers: corsHeaders }
        )
    }

    return NextResponse.json(response, { headers: corsHeaders })

  } catch (error) {
    console.error('[Admin] Action error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    )
  }
}

// Get active bans
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (userId) {
      // Check specific user ban
      const ban = await checkUserBan(userId)
      return NextResponse.json({
        success: true,
        banned: !!ban,
        banData: ban
      }, { headers: corsHeaders })
    }

    // Get all active bans
    const allBans = await readBans()
    const activeBans = allBans.filter(ban => 
      ban.expiresAt === null || ban.expiresAt > Date.now()
    )

    return NextResponse.json({
      success: true,
      bans: activeBans,
      total: activeBans.length
    }, { headers: corsHeaders })

  } catch (error) {
    console.error('[Admin] Get bans error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    )
  }
} 
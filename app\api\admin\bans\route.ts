import { NextRequest, NextResponse } from 'next/server'
import { checkUserBan } from '../actions/route'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
}

export async function OPTIONS() {
  return new Response(null, { status: 200, headers: corsHeaders })
}

// Check if a user is banned
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID required' },
        { status: 400, headers: corsHeaders }
      )
    }

    const banData = await checkUserBan(userId)
    
    if (banData) {
      const timeRemaining = banData.expiresAt ? banData.expiresAt - Date.now() : null
      const isExpired = timeRemaining !== null && timeRemaining <= 0

      return NextResponse.json({
        success: true,
        banned: !isExpired,
        banData: isExpired ? null : banData,
        timeRemaining: isExpired ? 0 : timeRemaining
      }, { headers: corsHeaders })
    }

    return NextResponse.json({
      success: true,
      banned: false,
      banData: null,
      timeRemaining: 0
    }, { headers: corsHeaders })

  } catch (error) {
    console.error('[Admin] Ban check error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    )
  }
} 
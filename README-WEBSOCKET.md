# WebSocket Connection System

This system provides real-time monitoring of Roblox script executions and active connections.

## Features

- **Real-time Connection Monitoring**: See who's connected and executing scripts in real-time
- **WebSocket Communication**: Live updates without page refreshing
- **POST/PATCH API**: RESTful endpoints for connection management
- **Lua Integration**: Ready-to-use Roblox script for client connections
- **Statistics Tracking**: Monitor daily executions, peak concurrent users, and more

## Setup

### 1. Environment Configuration

Add the following to your `.env.local` file:

```env
NEXT_PUBLIC_WEBSOCKET_URL=wss://your-domain.com
```

### 2. WebSocket Server

The WebSocket server is integrated with your Next.js application. It automatically starts when you run your development or production server.

### 3. API Endpoints

#### POST /api/connections
Create a new connection when a script starts executing.

**Request Body:**
```json
{
  "userId": "string",
  "scriptId": "string",
  "hwid": "string",
  "discordId": "string (optional)",
  "gameInfo": {
    "gameId": "string",
    "placeName": "string",
    "placeId": "string"
  },
  "executorInfo": {
    "name": "string",
    "version": "string"
  }
}
```

**Response:**
```json
{
  "success": true,
  "connectionId": "string",
  "message": "Connection established",
  "data": { ... }
}
```

#### PATCH /api/connections/[connectionId]
Update an existing connection (heartbeat/ping).

**Request Body:**
```json
{
  "lastPing": "number",
  "gameInfo": { ... },
  "playerPosition": { ... }
}
```

#### DELETE /api/connections?connectionId=[id]
Remove a connection when the script stops or player leaves.

### 4. Lua Script Integration

1. Copy the script from `scripts/luarmor-websocket.lua`
2. Update the configuration variables:
   - `API_URL`: Your domain's API endpoint
   - `WEBSOCKET_URL`: Your WebSocket server URL
   - `SCRIPT_ID`: Your Luarmor script ID

3. The script will automatically:
   - Connect when executed
   - Send heartbeats every 30 seconds
   - Update game information when changing places
   - Clean up on disconnect

### 5. Dashboard Usage

Navigate to the **Connections** page in your admin dashboard to see:

- **Live Connections**: Currently connected users
- **Executions Today**: Total script runs for the day
- **Peak Concurrent**: Maximum simultaneous connections
- **Recent Executions**: Last 100 script executions with details

## Technical Details

### WebSocket Events

**Server → Client:**
- `connection:new` - New connection established
- `connection:update` - Connection data updated
- `connection:remove` - Connection closed
- `execution:new` - New script execution
- `stats:update` - Statistics update

**Client → Server:**
- Standard Socket.IO connection events

### Security Considerations

1. **API Key**: The Lua script uses Luarmor's `script_key` for authentication
2. **CORS**: Configure appropriate CORS settings for your domain
3. **Rate Limiting**: Implement rate limiting on API endpoints
4. **HTTPS/WSS**: Always use secure connections in production

## Troubleshooting

### Connection Issues
- Verify WebSocket URL is correct in `.env.local`
- Check CORS settings if connections are blocked
- Ensure API endpoints are accessible from Roblox

### Data Not Updating
- Check browser console for WebSocket errors
- Verify server is emitting events correctly
- Ensure connection heartbeats are being sent

### Performance
- The system maintains last 100 executions in memory
- Connections are cleaned up automatically on disconnect
- Daily stats reset at midnight automatically 
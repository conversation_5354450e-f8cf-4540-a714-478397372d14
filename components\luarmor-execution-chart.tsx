'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart'
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, ResponsiveContainer, BarChart, Bar } from 'recharts'
import { useLuarmor } from '@/lib/luarmor-store'

export function LuarmorExecutionChart() {
  const { keyStats } = useLuarmor()

  if (!keyStats) {
    return null
  }

  const daysToShow = 31
  const allExecutions = keyStats.execution_data.executions
  
  // Extend or pad the data to 31 days
  const extendedData = Array.from({ length: daysToShow }, (_, index) => ({
    day: index === 0 ? 'Today' : index === 1 ? 'Yesterday' : `${index} days ago`,
    executions: allExecutions[allExecutions.length - 1 - index] || 0,
    dayIndex: index
  })).reverse()
  
  const chartData = extendedData

  const chartConfig = {
    executions: {
      label: 'Executions',
      color: 'hsl(var(--chart-1))',
    },
  }

  const totalExecutions = extendedData.reduce((sum, item) => sum + item.executions, 0)
  const avgExecutions = Math.round(totalExecutions / daysToShow)
  const maxExecutions = Math.max(...extendedData.map(item => item.executions))

  return (
    <Card>
      <CardHeader>
        <CardTitle>Script Executions</CardTitle>
        <CardDescription>
          Daily execution activity over the last 31 days
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-chart-1">{totalExecutions.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Total (31 days)</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-chart-2">{avgExecutions.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Daily Average</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-chart-3">{maxExecutions.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Peak Day</div>
          </div>
        </div>
        
        <ChartContainer config={chartConfig} className="min-h-[200px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData}>
              <XAxis 
                dataKey="day" 
                tick={{ fontSize: 11 }}
                axisLine={false}
                tickLine={false}
                interval={5}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
              />
              <ChartTooltip 
                content={<ChartTooltipContent />}
                cursor={{ fill: 'hsl(var(--muted))', opacity: 0.5 }}
              />
              <Bar 
                dataKey="executions" 
                fill="var(--color-executions)" 
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
} 
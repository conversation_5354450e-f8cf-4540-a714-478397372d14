<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Carousel Test</title>
    <style>
        body {
            margin: 0;
            padding: 50px;
            background: linear-gradient(135deg, #1e293b, #334155, #475569);
            font-family: Arial, sans-serif;
            color: white;
        }
        
        .carousel-container {
            perspective: 1500px;
            perspective-origin: center center;
            margin: 50px auto;
            width: 100%;
            max-width: 1200px;
        }
        
        .carousel-track {
            transform-style: preserve-3d;
            position: relative;
            height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .card {
            position: absolute;
            width: 320px;
            height: 480px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2));
            border: 1px solid rgba(59, 130, 246, 0.4);
            border-radius: 24px;
            backdrop-filter: blur(8px);
            transform-style: preserve-3d;
            backface-visibility: hidden;
            transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }
        
        .card-center {
            transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1);
            z-index: 30;
        }
        
        .card-left {
            transform: translateX(-320px) translateZ(0px) rotateY(-15deg) scale(0.9);
            z-index: 20;
        }
        
        .card-right {
            transform: translateX(320px) translateZ(0px) rotateY(15deg) scale(0.9);
            z-index: 20;
        }
        
        .card-far-left {
            transform: translateX(-640px) translateZ(-100px) rotateY(-25deg) scale(0.7);
            z-index: 10;
            opacity: 0.4;
        }
        
        .card-far-right {
            transform: translateX(640px) translateZ(-100px) rotateY(25deg) scale(0.7);
            z-index: 10;
            opacity: 0.4;
        }
        
        .controls {
            text-align: center;
            margin-top: 30px;
        }
        
        button {
            background: rgba(59, 130, 246, 0.8);
            border: none;
            color: white;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: rgba(59, 130, 246, 1);
        }
        
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; margin-bottom: 50px;">3D Card Carousel Test</h1>
    
    <div class="carousel-container">
        <div class="carousel-track" id="carousel">
            <div class="card card-far-left" data-position="-2">Card 1</div>
            <div class="card card-left" data-position="-1">Card 2</div>
            <div class="card card-center" data-position="0">Card 3</div>
            <div class="card card-right" data-position="1">Card 4</div>
            <div class="card card-far-right" data-position="2">Card 5</div>
        </div>
    </div>
    
    <div class="controls">
        <button onclick="prevCard()">← Previous</button>
        <button onclick="nextCard()">Next →</button>
    </div>
    
    <script>
        let currentIndex = 2; // Start with card 3 in center
        const totalCards = 5;
        let isAnimating = false;
        
        function updateCardPositions() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                const position = index - currentIndex;
                card.className = 'card';
                
                if (position === 0) card.classList.add('card-center');
                else if (position === -1) card.classList.add('card-left');
                else if (position === 1) card.classList.add('card-right');
                else if (position === -2) card.classList.add('card-far-left');
                else if (position === 2) card.classList.add('card-far-right');
                else card.style.opacity = '0';
                
                card.setAttribute('data-position', position);
            });
        }
        
        function nextCard() {
            if (isAnimating) return;
            isAnimating = true;
            currentIndex = (currentIndex + 1) % totalCards;
            updateCardPositions();
            setTimeout(() => { isAnimating = false; }, 700);
        }
        
        function prevCard() {
            if (isAnimating) return;
            isAnimating = true;
            currentIndex = (currentIndex - 1 + totalCards) % totalCards;
            updateCardPositions();
            setTimeout(() => { isAnimating = false; }, 700);
        }
        
        // Initialize
        updateCardPositions();
    </script>
</body>
</html>

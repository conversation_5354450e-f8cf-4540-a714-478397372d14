'use client'

import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { MoreHorizontal, Search, UserPlus, RotateCcw, Ban, Link, Filter, X, Download } from 'lucide-react'
import { useLuarmor } from '@/lib/luarmor-store'
import { LuarmorUser } from '@/lib/luarmor-api'
import { useToast } from '@/hooks/use-toast'
import { CorsWarning } from '@/components/cors-warning'

function UserStatusBadge({ status, banned }: { status: string; banned: number }) {
  if (banned === 1) {
    return <Badge variant="destructive">Banned</Badge>
  }
  
  switch (status) {
    case 'active':
      return <Badge variant="default">Active</Badge>
    case 'reset':
      return <Badge variant="secondary">Reset</Badge>
    case 'banned':
      return <Badge variant="destructive">Banned</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

function UserActionMenu({ user }: { user: LuarmorUser }) {
  const { api, selectedProject, refreshUsers } = useLuarmor()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)

  const handleResetHwid = async () => {
    if (!api || !selectedProject) return
    
    setLoading(true)
    try {
      await api.resetUserHwid(selectedProject, user.user_key)
      toast({ title: 'Success', description: 'HWID reset successfully' })
      refreshUsers()
    } catch (error) {
      toast({ title: 'Error', description: 'Failed to reset HWID', variant: 'destructive' })
    }
    setLoading(false)
  }

  const handleBanUser = async () => {
    if (!api || !selectedProject) return
    
    setLoading(true)
    try {
      await api.blacklistUser(selectedProject, user.user_key, 'Banned from dashboard')
      toast({ title: 'Success', description: 'User banned successfully' })
      refreshUsers()
    } catch (error) {
      toast({ title: 'Error', description: 'Failed to ban user', variant: 'destructive' })
    }
    setLoading(false)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={handleResetHwid} disabled={loading}>
          <RotateCcw className="mr-2 h-4 w-4" />
          Reset HWID
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleBanUser} disabled={loading || user.banned === 1}>
          <Ban className="mr-2 h-4 w-4" />
          Ban User
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

function CreateUserDialog() {
  const { api, selectedProject, refreshUsers } = useLuarmor()
  const { toast } = useToast()
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    identifier: '',
    discord_id: '',
    note: '',
    key_days: ''
  })

  const handleSubmit = async () => {
    if (!api || !selectedProject) return

    setLoading(true)
    try {
      const payload: any = {}
      if (formData.identifier) payload.identifier = formData.identifier
      if (formData.discord_id) payload.discord_id = formData.discord_id
      if (formData.note) payload.note = formData.note
      if (formData.key_days) payload.key_days = parseInt(formData.key_days)

      const result = await api.createUser(selectedProject, payload)
      toast({ 
        title: 'Success', 
        description: `User created with key: ${result.user_key}` 
      })
      setOpen(false)
      setFormData({ identifier: '', discord_id: '', note: '', key_days: '' })
      refreshUsers()
    } catch (error) {
      toast({ title: 'Error', description: 'Failed to create user', variant: 'destructive' })
    }
    setLoading(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <UserPlus className="mr-2 h-4 w-4" />
          Add User
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New User</DialogTitle>
          <DialogDescription>
            Generate a new user key with optional pre-configured settings
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="identifier">HWID (Optional)</Label>
            <Input
              id="identifier"
              value={formData.identifier}
              onChange={(e) => setFormData(prev => ({ ...prev, identifier: e.target.value }))}
              placeholder="Hardware identifier"
            />
          </div>
          <div>
            <Label htmlFor="discord_id">Discord ID (Optional)</Label>
            <Input
              id="discord_id"
              value={formData.discord_id}
              onChange={(e) => setFormData(prev => ({ ...prev, discord_id: e.target.value }))}
              placeholder="Discord user ID"
            />
          </div>
          <div>
            <Label htmlFor="key_days">Key Duration (Days)</Label>
            <Input
              id="key_days"
              type="number"
              value={formData.key_days}
              onChange={(e) => setFormData(prev => ({ ...prev, key_days: e.target.value }))}
              placeholder="Leave empty for unlimited"
            />
          </div>
          <div>
            <Label htmlFor="note">Note (Optional)</Label>
            <Textarea
              id="note"
              value={formData.note}
              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}
              placeholder="Custom note for this user"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Creating...' : 'Create User'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export function LuarmorUsersTable() {
  const { users, loading, corsError, setCorsError } = useLuarmor()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [executionFilter, setExecutionFilter] = useState<string>('all')

  const filteredUsers = users.filter(user => {
    // Search filter
    const matchesSearch = 
      user.user_key.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.identifier.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.discord_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.note.toLowerCase().includes(searchTerm.toLowerCase())
    
    // Status filter
    let matchesStatus = true
    if (statusFilter === 'active') {
      matchesStatus = user.status === 'active' && user.banned === 0
    } else if (statusFilter === 'banned') {
      matchesStatus = user.banned === 1
    } else if (statusFilter === 'reset') {
      matchesStatus = user.status === 'reset'
    } else if (statusFilter === 'no-hwid') {
      matchesStatus = !user.identifier
    } else if (statusFilter === 'no-discord') {
      matchesStatus = !user.discord_id
    }
    
    // Execution filter
    let matchesExecution = true
    if (executionFilter === 'never') {
      matchesExecution = user.total_executions === 0
    } else if (executionFilter === 'low') {
      matchesExecution = user.total_executions > 0 && user.total_executions <= 10
    } else if (executionFilter === 'medium') {
      matchesExecution = user.total_executions > 10 && user.total_executions <= 100
    } else if (executionFilter === 'high') {
      matchesExecution = user.total_executions > 100
    }
    
    return matchesSearch && matchesStatus && matchesExecution
  })

  const formatDate = (timestamp: number) => {
    if (timestamp === -1 || timestamp === 0) return 'Never'
    return new Date(timestamp * 1000).toLocaleDateString()
  }

  const activeCount = filteredUsers.filter(u => u.status === 'active' && u.banned === 0).length
  const bannedCount = filteredUsers.filter(u => u.banned === 1).length
  const resetCount = filteredUsers.filter(u => u.status === 'reset').length
  const inactiveCount = filteredUsers.filter(u => u.total_executions === 0).length

  return (
    <>
      <div className="space-y-4">
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeCount}</div>
              <p className="text-xs text-muted-foreground">Currently active</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Banned Users</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{bannedCount}</div>
              <p className="text-xs text-muted-foreground">Blacklisted</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Reset HWID</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{resetCount}</div>
              <p className="text-xs text-muted-foreground">Awaiting setup</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Inactive</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-600">{inactiveCount}</div>
              <p className="text-xs text-muted-foreground">Never executed</p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Users Management</CardTitle>
                <CardDescription>
                  Manage your Luarmor users and their access
                </CardDescription>
              </div>
              <CreateUserDialog />
            </div>
          </CardHeader>
        <CardContent>
          <div className="space-y-4 mb-4">
            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Filters:</span>
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active Users</SelectItem>
                  <SelectItem value="banned">Banned Users</SelectItem>
                  <SelectItem value="reset">Reset HWID</SelectItem>
                  <SelectItem value="no-hwid">No HWID</SelectItem>
                  <SelectItem value="no-discord">No Discord</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={executionFilter} onValueChange={setExecutionFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Executions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Executions</SelectItem>
                  <SelectItem value="never">Never Executed</SelectItem>
                  <SelectItem value="low">1-10 Executions</SelectItem>
                  <SelectItem value="medium">11-100 Executions</SelectItem>
                  <SelectItem value="high">100+ Executions</SelectItem>
                </SelectContent>
              </Select>
              
              {(statusFilter !== 'all' || executionFilter !== 'all' || searchTerm) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setStatusFilter('all')
                    setExecutionFilter('all')
                    setSearchTerm('')
                  }}
                >
                  <X className="h-3 w-3 mr-1" />
                  Clear Filters
                </Button>
              )}
              
              <div className="ml-auto flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const csv = [
                      ['User Key', 'Status', 'HWID', 'Discord', 'Executions', 'Last Reset', 'Note'].join(','),
                      ...filteredUsers.map(user => [
                        user.user_key,
                        user.banned ? 'Banned' : user.status,
                        user.identifier || '',
                        user.discord_id || '',
                        user.total_executions,
                        new Date(user.last_reset * 1000).toISOString(),
                        user.note || ''
                      ].join(','))
                    ].join('\n')
                    
                    const blob = new Blob([csv], { type: 'text/csv' })
                    const url = window.URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = `luarmor-users-${new Date().toISOString().split('T')[0]}.csv`
                    a.click()
                  }}
                >
                  <Download className="h-3 w-3 mr-1" />
                  Export CSV
                </Button>
                
                <span className="text-sm text-muted-foreground">
                  Showing {filteredUsers.length} of {users.length} users
                </span>
              </div>
            </div>
          </div>
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User Key</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>HWID</TableHead>
                  <TableHead>Discord</TableHead>
                  <TableHead>Executions</TableHead>
                  <TableHead>Last Reset</TableHead>
                  <TableHead>Note</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      Loading users...
                    </TableCell>
                  </TableRow>
                ) : filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      No users found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => (
                    <TableRow key={user.user_key || 'unknown'}>
                      <TableCell className="font-mono text-sm">
                        {user.user_key ? user.user_key.substring(0, 8) + '...' : 'N/A'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <UserStatusBadge status={user.status} banned={user.banned} />
                          {user.total_resets > 0 && (
                            <Badge variant="outline" className="text-xs">
                              {user.total_resets} resets
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {user.identifier || 'Not set'}
                      </TableCell>
                      <TableCell>
                        {user.discord_id || 'Not linked'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span>{user.total_executions}</span>
                          {user.total_executions === 0 && (
                            <Badge variant="secondary" className="text-xs">Inactive</Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(user.last_reset)}</TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {user.note || '-'}
                      </TableCell>
                      <TableCell>
                        <UserActionMenu user={user} />
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      </div>
      <CorsWarning isVisible={corsError} onClose={() => setCorsError(false)} />
    </>
  )
} 
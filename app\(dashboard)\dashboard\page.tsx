'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { LuarmorSetup } from "@/components/luarmor-setup"
import { LuarmorStatsCards } from "@/components/luarmor-stats"
import { LuarmorExecutionChart } from "@/components/luarmor-execution-chart"
import { useLuarmor } from "@/lib/luarmor-store"

export default function DashboardPage() {
  const { apiKey, keyDetails } = useLuarmor()

  if (!apiKey || !keyDetails) {
    return (
      <div className="flex flex-col gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Luarmor Dashboard</h1>
          <p className="text-muted-foreground">Connect your Luarmor API to access your dashboard data.</p>
        </div>
        <div className="flex items-center justify-center min-h-[400px]">
          <LuarmorSetup />
        </div>
      </div>
    )
  }

  const selectedProject = keyDetails.projects[0]
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Luarmor Dashboard</h1>
          <p className="text-muted-foreground">
            Project: {selectedProject?.name || 'No project selected'}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={keyDetails.enabled ? "default" : "destructive"}>
            {keyDetails.enabled ? "Active" : "Disabled"}
          </Badge>
          <Badge variant="outline">{keyDetails.plan.toUpperCase()}</Badge>
        </div>
      </div>

      <LuarmorStatsCards />
      
      <div className="grid gap-4 grid-cols-1 lg:grid-cols-7">
        <div className="lg:col-span-4">
          <LuarmorExecutionChart />
        </div>
        
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
            <CardDescription>
              Your Luarmor account details and settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Email</span>
              <span className="text-sm text-muted-foreground">{keyDetails.email}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Discord ID</span>
              <span className="text-sm text-muted-foreground">
                {keyDetails.discord_id || 'Not linked'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Registered</span>
              <span className="text-sm text-muted-foreground">
                {new Date(keyDetails.registered_at * 1000).toLocaleDateString()}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Expires</span>
              <span className="text-sm text-muted-foreground">
                {keyDetails.expires_at === -1 ? 'Never' : new Date(keyDetails.expires_at * 1000).toLocaleDateString()}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {selectedProject && selectedProject.scripts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Scripts Overview</CardTitle>
            <CardDescription>
              Available scripts in {selectedProject.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {selectedProject.scripts.map((script) => (
                <div key={script.script_id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{script.script_name}</p>
                    <p className="text-xs text-muted-foreground">Version {script.script_version}</p>
                  </div>
                  <div className="flex gap-2">
                    {script.ffa && <Badge variant="secondary">FFA</Badge>}
                    {script.silent && <Badge variant="outline">Silent</Badge>}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { MoreHorizontal, UserX, Ban, Megaphone } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface Connection {
  id: string
  username: string
  gameId: string
  key: string
  connectedAt: string
  status: string
}

interface ConnectionActionDialogProps {
  connection: Connection
}

export function ConnectionActionDialog({ connection }: ConnectionActionDialogProps) {
  const [open, setOpen] = useState(false)
  const [selectedAction, setSelectedAction] = useState<string | null>(null)
  const [message, setMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleAction = async (action: string) => {
    setIsLoading(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    console.log(`${action} action for user:`, connection.username)
    if (action === "announcement" && message) {
      console.log("Message:", message)
    }

    setIsLoading(false)
    setOpen(false)
    setSelectedAction(null)
    setMessage("")
  }

  const resetDialog = () => {
    setSelectedAction(null)
    setMessage("")
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        setOpen(newOpen)
        if (!newOpen) resetDialog()
      }}
    >
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Open actions menu</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Connection Actions</DialogTitle>
          <DialogDescription>
            Choose an action for user: <strong>{connection.username}</strong>
          </DialogDescription>
        </DialogHeader>

        {!selectedAction ? (
          <div className="grid gap-4 py-4">
            <div className="grid gap-3">
              <Button
                variant="outline"
                className="justify-start h-auto p-4 hover:bg-orange-50 hover:border-orange-200 dark:hover:bg-orange-950/20 bg-transparent"
                onClick={() => setSelectedAction("kick")}
              >
                <UserX className="h-5 w-5 mr-3 text-orange-600" />
                <div className="text-left">
                  <div className="font-medium">Kick User</div>
                  <div className="text-sm text-muted-foreground">Disconnect the user from the server</div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="justify-start h-auto p-4 hover:bg-red-50 hover:border-red-200 dark:hover:bg-red-950/20 bg-transparent"
                onClick={() => setSelectedAction("ban")}
              >
                <Ban className="h-5 w-5 mr-3 text-red-600" />
                <div className="text-left">
                  <div className="font-medium">Ban User</div>
                  <div className="text-sm text-muted-foreground">Permanently ban the user from the server</div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="justify-start h-auto p-4 hover:bg-blue-50 hover:border-blue-200 dark:hover:bg-blue-950/20 bg-transparent"
                onClick={() => setSelectedAction("announcement")}
              >
                <Megaphone className="h-5 w-5 mr-3 text-blue-600" />
                <div className="text-left">
                  <div className="font-medium">Send Announcement</div>
                  <div className="text-sm text-muted-foreground">Send a message to this user</div>
                </div>
              </Button>
            </div>
          </div>
        ) : (
          <div className="grid gap-4 py-4">
            {selectedAction === "kick" && (
              <Alert className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
                <UserX className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-orange-800 dark:text-orange-200">
                  This will immediately disconnect <strong>{connection.username}</strong> from the server. They can
                  reconnect afterwards.
                </AlertDescription>
              </Alert>
            )}

            {selectedAction === "ban" && (
              <Alert className="border-red-200 bg-red-50 dark:bg-red-950/20">
                <Ban className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800 dark:text-red-200">
                  This will permanently ban <strong>{connection.username}</strong> from the server. This action cannot
                  be undone from this interface.
                </AlertDescription>
              </Alert>
            )}

            {selectedAction === "announcement" && (
              <div className="space-y-4">
                <Alert className="border-blue-200 bg-blue-50 dark:bg-blue-950/20">
                  <Megaphone className="h-4 w-4 text-blue-600" />
                  <AlertDescription className="text-blue-800 dark:text-blue-200">
                    Send a message to <strong>{connection.username}</strong>
                  </AlertDescription>
                </Alert>
                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <Textarea
                    id="message"
                    placeholder="Enter your announcement message..."
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>
              </div>
            )}
          </div>
        )}

        <DialogFooter>
          {selectedAction ? (
            <div className="flex gap-2 w-full">
              <Button variant="outline" onClick={resetDialog} disabled={isLoading}>
                Back
              </Button>
              <Button
                onClick={() => handleAction(selectedAction)}
                disabled={isLoading || (selectedAction === "announcement" && !message.trim())}
                className={
                  selectedAction === "kick"
                    ? "bg-orange-600 hover:bg-orange-700"
                    : selectedAction === "ban"
                      ? "bg-red-600 hover:bg-red-700"
                      : "bg-blue-600 hover:bg-blue-700"
                }
              >
                {isLoading
                  ? "Processing..."
                  : selectedAction === "kick"
                    ? "Kick User"
                    : selectedAction === "ban"
                      ? "Ban User"
                      : "Send Announcement"}
              </Button>
            </div>
          ) : (
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

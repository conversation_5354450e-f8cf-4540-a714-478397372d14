{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@clerk/nextjs": "^6.10.2", "@hookform/resolvers": "^3.3.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-table": "^8.10.7", "autoprefixer": "10.4.16", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "1.0.0", "date-fns": "^2.30.0", "embla-carousel-react": "^8.0.0", "framer-motion": "^10.16.4", "input-otp": "^1.2.2", "lucide-react": "^0.451.0", "next": "15.2.4", "next-themes": "^0.4.4", "node-fetch": "^3.3.2", "react": "18.3.1", "react-day-picker": "8.10.1", "react-dom": "18.3.1", "react-hook-form": "^7.47.0", "react-resizable-panels": "^2.0.16", "recharts": "^2.8.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.2.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "vaul": "^0.9.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "20.8.10", "@types/react": "18.2.33", "@types/react-dom": "18.2.14", "eslint": "8.52.0", "eslint-config-next": "15.0.3", "postcss": "8.4.31", "tailwindcss": "3.3.5", "typescript": "5.2.2"}}
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;

    /* Sidebar specific variables */
    --sidebar: 0 0% 100%;
    --sidebar-foreground: 240 10% 3.9%;
    --sidebar-muted: 240 4.8% 95.9%;
    --sidebar-muted-foreground: 240 3.8% 46.1%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 240 5.9% 90%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;

    /* Sidebar specific variables */
    --sidebar: 240 10% 3.9%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-muted: 240 3.7% 15.9%;
    --sidebar-muted-foreground: 240 5% 64.9%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 240 3.7% 15.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
  }
  
  /* Enhanced smooth transitions for theme changes */
  *,
  *::before,
  *::after {
    transition: 
      background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      border-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      fill 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      stroke 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Specific transitions for common UI elements */
  [data-radix-collection-item],
  [role="menuitem"],
  [role="button"],
  button,
  input,
  select,
  textarea {
    transition: 
      background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Ensure cards and containers transition smoothly */
  [class*="card"],
  [class*="dialog"],
  [class*="popover"],
  [class*="dropdown"],
  [class*="sheet"] {
    transition: 
      background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      border-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Sidebar styles with smooth transitions */
.bg-sidebar {
  background-color: hsl(var(--sidebar));
  transition: background-color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.text-sidebar-foreground {
  color: hsl(var(--sidebar-foreground));
  transition: color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.bg-sidebar-accent {
  background-color: hsl(var(--sidebar-accent));
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.text-sidebar-accent-foreground {
  color: hsl(var(--sidebar-accent-foreground));
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Additional theme transition styles */
svg {
  transition: 
    fill 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    stroke 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Chart and graph elements */
.recharts-wrapper,
[class*="chart"] {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth transitions for any backdrop or overlay */
[data-state],
[data-side] {
  transition: 
    background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Add responsive utility classes */
@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Enhanced 3D Carousel Perspective and Transform Styles */
.carousel-container {
  perspective: 2000px;
  perspective-origin: center center;
  position: relative;
}

.carousel-track {
  transform-style: preserve-3d;
  position: relative;
}

.game-card {
  transform-style: preserve-3d;
  backface-visibility: hidden;
  will-change: transform, opacity;
}

/* Ensure all card elements support 3D transforms */
.carousel-track > div {
  transform-style: preserve-3d;
}

.carousel-track > div > div {
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

/* Updated for new positioning system */

/* Legacy card positioning classes - now using card-position-* classes */

/* Smooth 3D Carousel Transition States */
.carousel-card {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
  backface-visibility: hidden;
  will-change: transform, opacity;
}

.card-exiting-left {
  animation: exit-to-left 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
}

.card-exiting-right {
  animation: exit-to-right 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
}

/* Enhanced 3D Card Positioning with Smooth Transitions */
.card-position-center {
  transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1) !important;
  z-index: 30;
  opacity: 1;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform-style: preserve-3d;
  will-change: transform, opacity;
}

.card-position-left {
  transform: translateX(-320px) translateZ(0px) rotateY(-15deg) scale(0.9) !important;
  z-index: 20;
  opacity: 1;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform-style: preserve-3d;
  will-change: transform, opacity;
}

.card-position-right {
  transform: translateX(320px) translateZ(0px) rotateY(15deg) scale(0.9) !important;
  z-index: 20;
  opacity: 1;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform-style: preserve-3d;
  will-change: transform, opacity;
}

.card-position-far-left {
  transform: translateX(-640px) translateZ(-100px) rotateY(-25deg) scale(0.7) !important;
  z-index: 10;
  opacity: 0.4;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform-style: preserve-3d;
  will-change: transform, opacity;
}

.card-position-far-right {
  transform: translateX(640px) translateZ(-100px) rotateY(25deg) scale(0.7) !important;
  z-index: 10;
  opacity: 0.4;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform-style: preserve-3d;
  will-change: transform, opacity;
}

/* Hidden cards (off-screen) */
.card-position-hidden-left {
  transform: translateX(-960px) translateZ(-200px) rotateY(-35deg) scale(0.5) !important;
  z-index: 5;
  opacity: 0;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform-style: preserve-3d;
  will-change: transform, opacity;
}

.card-position-hidden-right {
  transform: translateX(960px) translateZ(-200px) rotateY(35deg) scale(0.5) !important;
  z-index: 5;
  opacity: 0;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform-style: preserve-3d;
  will-change: transform, opacity;
}

@keyframes card-slide-in {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.card-enter {
  animation: card-slide-in 0.6s ease-out forwards;
}

@keyframes shine {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes shine-wave {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-shine {
  background-size: 200% auto;
  animation: shine 3s linear infinite;
}

.animate-shine-wave {
  animation: shine-wave 3s ease-in-out infinite;
}

/* Enhanced Smooth Hover Effects for 3D Cards */
.card-position-center:hover {
  transform: translateX(0) translateZ(120px) rotateY(0deg) scale(1.15) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 30px 60px rgba(59, 130, 246, 0.3) !important;
}

.card-position-left:hover {
  transform: translateX(-320px) translateZ(20px) rotateY(-10deg) scale(0.95) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.card-position-right:hover {
  transform: translateX(320px) translateZ(20px) rotateY(10deg) scale(0.95) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Debug: Visual indicators for transitions */
.card-position-center {
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
}

.card-position-left,
.card-position-right {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.card-position-far-left,
.card-position-far-right {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Subtle floating animation for center card */
@keyframes float-center {
  0%, 100% {
    transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1) translateY(0px);
  }
  50% {
    transform: translateX(0) translateZ(100px) rotateY(0deg) scale(1.1) translateY(-5px);
  }
}

.card-position-center {
  animation: float-center 4s ease-in-out infinite;
}

@keyframes exit-to-left {
  0% {
    transform: translateX(-320px) translateZ(0px) rotateY(-15deg) scale(0.9);
    opacity: 1;
  }
  25% {
    transform: translateX(-400px) translateZ(-25px) rotateY(-60deg) scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: translateX(-480px) translateZ(-50px) rotateY(-105deg) scale(0.75);
    opacity: 0.6;
  }
  75% {
    transform: translateX(-560px) translateZ(-75px) rotateY(-150deg) scale(0.7);
    opacity: 0.5;
  }
  100% {
    transform: translateX(-640px) translateZ(-100px) rotateY(-25deg) scale(0.7);
    opacity: 0.4;
  }
}

@keyframes exit-to-right {
  0% {
    transform: translateX(320px) translateZ(0px) rotateY(15deg) scale(0.9);
    opacity: 1;
  }
  25% {
    transform: translateX(400px) translateZ(-25px) rotateY(60deg) scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: translateX(480px) translateZ(-50px) rotateY(105deg) scale(0.75);
    opacity: 0.6;
  }
  75% {
    transform: translateX(560px) translateZ(-75px) rotateY(150deg) scale(0.7);
    opacity: 0.5;
  }
  100% {
    transform: translateX(640px) translateZ(-100px) rotateY(25deg) scale(0.7);
    opacity: 0.4;
  }
}

'use client'

import { LuarmorSetup } from "@/components/luarmor-setup"
import { LuarmorUsersTable } from "@/components/luarmor-users-table"
import { useLuarmor } from "@/lib/luarmor-store"

export default function UsersPage() {
  const { apiKey, keyDetails } = useLuarmor()

  if (!apiKey || !keyDetails) {
    return (
      <div className="flex flex-col gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground">Connect your Luarmor API to manage users.</p>
        </div>
        <div className="flex items-center justify-center min-h-[400px]">
          <LuarmorSetup />
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-4">
      <div>
        <h1 className="text-2xl md:text-3xl font-bold tracking-tight">User Management</h1>
        <p className="text-muted-foreground">
          Manage users for {keyDetails.projects[0]?.name || 'your project'}
        </p>
      </div>
      
      <LuarmorUsersTable />
    </div>
  )
}

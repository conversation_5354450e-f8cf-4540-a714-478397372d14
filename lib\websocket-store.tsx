'use client'

import * as React from 'react'

interface ExecutionData {
  userId: string
  scriptId: string
  timestamp: number
  hwid: string
  discordId?: string
  gameId?: string
  placeName?: string
  executorName?: string
}

interface ConnectionData {
  id: string
  userId: string
  scriptId: string
  hwid: string
  connectedAt: number
  lastPing: number
  status: string
  ping?: {
    current: number
    average: number
    min: number
    max: number
    history: number[]
  }
  gameInfo?: {
    gameId: string
    placeName: string
    placeId: string
  }
  executorInfo?: {
    name: string
    version: string
  }
}

interface ConnectionResponse {
  success: boolean
  connections: ConnectionData[]
  total: number
  active: number
}

interface WebSocketStore {
  connected: boolean
  activeConnections: ConnectionData[]
  recentExecutions: ExecutionData[]
  totalExecutionsToday: number
  peakConcurrent: number
  loading: boolean
  error: string | null
  refreshData: () => Promise<void>
}

const WebSocketContext = React.createContext<WebSocketStore | null>(null)

export function WebSocketProvider({ children }: { children: React.ReactNode }) {
  const [connected, setConnected] = React.useState(true) // API polling is "connected"
  const [activeConnections, setActiveConnections] = React.useState<ConnectionData[]>([])
  const [recentExecutions, setRecentExecutions] = React.useState<ExecutionData[]>([])
  const [totalExecutionsToday, setTotalExecutionsToday] = React.useState(0)
  const [peakConcurrent, setPeakConcurrent] = React.useState(0)
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)

  const fetchConnections = React.useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      console.log('[Dashboard] Fetching connections...')
      const response = await fetch('/api/connections', {
        cache: 'no-store'
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data: ConnectionResponse = await response.json()
      console.log('[Dashboard] Connections fetched:', data)
      
      if (data.success) {
        const connections = data.connections || []
        setActiveConnections(connections)
        setPeakConcurrent(Math.max(peakConcurrent, connections.length))
        
        // Calculate executions today (mock data for now)
        const today = new Date().toDateString()
        const todayConnections = connections.filter(conn => 
          new Date(conn.connectedAt).toDateString() === today
        )
        setTotalExecutionsToday(todayConnections.length)
        
        // Generate mock recent executions from connections
        const mockExecutions: ExecutionData[] = connections.slice(0, 10).map(conn => ({
          userId: conn.userId,
          scriptId: conn.scriptId,
          timestamp: conn.connectedAt,
          hwid: conn.hwid,
          gameId: conn.gameInfo?.gameId,
          placeName: conn.gameInfo?.placeName,
          executorName: conn.executorInfo?.name
        }))
        setRecentExecutions(mockExecutions)
        
        setConnected(true)
      } else {
        throw new Error('Failed to fetch connections')
      }
    } catch (err) {
      console.error('[Dashboard] Failed to fetch connections:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      setConnected(false)
    } finally {
      setLoading(false)
    }
  }, [peakConcurrent])

  // Poll for connections every 3 seconds
  React.useEffect(() => {
    fetchConnections() // Initial fetch
    
    const interval = setInterval(() => {
      fetchConnections()
    }, 3000)
    
    return () => clearInterval(interval)
  }, [fetchConnections])

  const value: WebSocketStore = {
    connected,
    activeConnections,
    recentExecutions,
    totalExecutionsToday,
    peakConcurrent,
    loading,
    error,
    refreshData: fetchConnections
  }

  return React.createElement(
    WebSocketContext.Provider,
    { value },
    children
  )
}

export function useWebSocket() {
  const context = React.useContext(WebSocketContext)
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider')
  }
  return context
} 